cat1:
  - Active
  - Passive
  - Electro-Mechanical
  - Magnetic
  - Mechanical
  - Raw-Material
  - Other
pur:
  - <PERSON><PERSON><PERSON>
  - <PERSON>.<PERSON>
  - <PERSON><PERSON>.<PERSON><PERSON>
  - <PERSON>.<PERSON><PERSON>
  - <PERSON>.<PERSON><PERSON><PERSON>.<PERSON>
  - <PERSON><PERSON>.<PERSON>
  - <PERSON>.Yl.<PERSON>
  - <PERSON>.chen
bom_owner:
  - <PERSON>.Yl.<PERSON>
  - <PERSON><PERSON><PERSON>
  - <PERSON><PERSON><PERSON>.<PERSON>
  <PERSON> <PERSON><PERSON>.Chen
  - <PERSON><PERSON><PERSON>.<PERSON>
  - <PERSON><PERSON><PERSON>.<PERSON>
  - <PERSON><PERSON><PERSON>.<PERSON>
  - <PERSON><PERSON><PERSON>.Gu
depts:
  - SUP_SUP
  - DCBU_DCBU
  - ADP_ADP
  - NBE_IPS
  - AMP_AMP
  - ATI_ATI
  - EVCS_MODULE
  - IDC_IDC
  - MSBU_MSBU
  - SAFETY_SAFETY
  - HPRT_HPRT
  - DPEC_DPEC
  - EISBG_PCSBD
  - PQC_PQC
  - EISBG_WPBU
  - PMSBD_AUTO
  - EVCS_SYSTEM
  - BABG_LGT
  - TPS_TPS
  - DES_CDBU
  - APE_APE
  - LGT_LGT
  - SPA_PVIBU
  - RTP_PCSBD
  - DES_IMBU
  - NBE_HVG
  - PMSB<PERSON>_PMSBD
  - ESPBD_ESPBD
hz_dept:
  - DES_CDBU
  - DES_IMBU
  - APE_APE
  - LGT_LGT
  - SPA_PVIBU
  - RTP_PCSBD
  - SUP_SUP
wh_dept:
  - DES_CDBU
  - DCBU_DCBU
  - ESPBD_ESPBD
  - SUP_SUP
cq_dept:
  - AMP_AMP
nj_dept:
  - ADP_ADP
sh_dept:
  - DCBU_DCBU
  - ADP_ADP
  - NBE_IPS
  - NBE_HVG
  - AMP_AMP
  - ATI_ATI
  - EVCS_MODULE
  - EVCS_SYSTEM
  - IDC_IDC
  - MSBU_MSBU
  - SAFETY_SAFETY
  - HPRT_HPRT
  - DPEC_DPEC
  - EISBG_PCSBD
  - EISBG_WPBU
  - PQC_PQC
  - BABG_LGT
  - TPS_TPS
  - PMSBD_PMSBD
  - EVCM_EVCM
  - SUP_SUP
sh_mail:
  - bo.sm.wang
  - kuangyi.gu
  - mona.zhang
  - wenwen.zang
  - qian.yuan
  - yan.gczy.zhang
  - xiao.cui
  - vicky.zhang
  - yingzhi.zhang
  - ping.ji
  - caiyan.wang
  - xiaoyang.yan
hz_mail:
  - qin.hong
  - weiming.li
  - lili.fang
  - cc.zhu
  - hq.cai
pcb_mail:
  - mona.zhang
  - bo.sm.wang
  - yanli.chen
  - jie.gong
  - shanliang.chen
  - lijun.liu
  - yukun.wang
  - weiming.li
smd_mail:
  - jie.gong
  - shanliang.chen
  - lijun.liu
  - yukun.wang
des_layout:
  - MINLEI.ZHONG
  - LOUIS.YANG
  - LYNNE.LI
  - Janah.zhao
  - HANK.DH.LU
  - MONA.JH.HUA
  - AMORS.ZHOU
  - CATHERINE.PENG
  - XIAOYE.LOO
  - FIONA.LEE
  - STERLING.ZHUO
  - Jamese.Wang
  - FELIX.ZHANG
  - bei.zheng
  - jessica.song
ape_layout:
  - Tina.liu
  - MARIA.CAO
  - Kerwin.Liu
  - HANCOCK.LIU
amp_lesson_admin:
  - vera.yan
  - changqing.dong
  - anne.chen
  - xinmei.han
  - yuanqing.wang
  - mason.xu
ce:
  - Yinyi.Gu
  - Xiaoli.Liu
  - Yi.Le
  - Jungen.Hu
  - Huanhuan.Yu
  - Huiyun.Jin
  - Biyu.Pd.Zhang
  - Xin.Shu
  - Ying.Gao
  - Yang.Zhao
  - Yingtian.Yt.He
  - Yaanna.Huang
  - Jinfeng.Zhang
  - Zihan.Ye.Yeh
ce_admin:
  - ying.gao
  - weiming.li
ce_status:
  open:
    - open
  ongoing:
    - ongoing
    - approve
  close:
    - close
kpi_depts:
  - DCBU_DCBU
  - ADP_ADP
  - NBE_IPS
  - NBE_HVG
  - AMP_AMP
  - ATI_ATI
  - EVCS_MODULE
  - IDC_IDC
  - HPRT_HPRT
  - DPEC_DPEC
  - EISBG_PCSBD
  - PQC_PQC
  - EISBG_WPBU
  - PMSBD_PMSBD
  - PMSBD_NONAUTO
  - EVCS_SYSTEM
  - BABG_LGT
  - UPS_UPS
  - TPS_TPS
  - DES_CDBU
  - DES_IMBU
  - APE_APE
  - LGT_LGT
  - SPA_PVIBU
  - SPA_EVCSBU
  - RTP_PCSBD
  - MES_MES
  - ESPBD_ESPBD
fa_owner:
  - "dept": "DCBU_DCBU"
    "cat1": "Passive"
    "ce_owner": "Ying.Gao"
  - "dept": "DCBU_DCBU"
    "cat1": "Active"
    "ce_owner": "Xin.Shu"
  - "dept": "DES_CDBU"
    "cat1": "Passive"
    "ce_owner": "Ying.Gao"
  - "dept": "DES_CDBU"
    "cat1": "Active"
    "ce_owner": "Xin.Shu"
  - "dept": "DES_IMBU"
    "cat1": "Passive"
    "ce_owner": "Ying.Gao"
  - "dept": "DES_IMBU"
    "cat1": "Active"
    "ce_owner": "Xin.Shu"
  - "dept": "AMP_AMP"
    "cat1": "Passive"
    "ce_owner": "Ying.Gao"
  - "dept": "AMP_AMP"
    "cat1": "Active"
    "ce_owner": "Xin.Shu"
  - "dept": "APE_APE"
    "cat1": "Passive"
    "ce_owner": "Ying.Gao"
  - "dept": "APE_APE"
    "cat1": "Active"
    "ce_owner": "Xin.Shu"
  - "dept": "ATI_ATI"
    "cat1": "Passive"
    "ce_owner": "Ying.Gao"
  - "dept": "ATI_ATI"
    "cat1": "Active"
    "ce_owner": "Xin.Shu"
nre_admin:
  - weiming.li
  - roby.liao
  - jolin.hou
  - ecco.lu
  - sophia.luo
  - mollie.zhao
  - harry.hh.huang
  - kai.dong
  - helen.hu
  - jaden.jiang
  - wilson.guo
  - tony.wang
  - gracy.wu
  - emily.li
  - winnie.zhu
  - alexander.dong
  - minami.guo
  - candy.he
  - mavis.hu
  - jasmine.zhang
  - haifeng.hao
  - yana.yu
  - rebecca.jin
  - faye.shen
  - benny.chai
  - jayden.yu
  - mila.ma
  - kathy.jiang
  - seanna.meng
  - jack.ding
  - rachel.lv
  - edana.feng
  - clary.wang
  - jack.ding
  - tobby.du
dummy_admin:
  - selene.wang
  - faye.shen
styles:
  "root": {"display": "flex", "flexDirection": "row", "alignItems": "center"}
  "label": {"width": 100, "fontSize": 15}
  "input": {"width": 180}
train_admin:
  - yan.wy.wang
  - weiming.li
  - lilian.wang
  - siying.meng
  - yue.zheng
hz_dip:
  - "i": "lili.fang"
    "x": 0
    "y": 0
    "w": 1
    "h": 2
    "static": true
  - "i": "cc.zhu"
    "x": 0
    "y": 2
    "w": 1
    "h": 2
    "static": true
  - "i": "zhibiao.ruan"
    "x": 0
    "y": 4
    "w": 1
    "h": 2
    "static": true
  - "i": "qin.hong"
    "x": 0
    "y": 6
    "w": 1
    "h": 2
    "static": true
dip_user:
  'HZ': ['Cc.Zhu', 'Lili.Fang', 'Zhibiao.Ruan', 'Qin.Hong', 'OutSource1', 'OutSource2']
  'SH': ['Taizhi.Tz.Huang', 'Nsc.Ni', 'Yyyayayanyan.Lu', 'Juanjuan.Fang', 'Lihualan.Lee',
    'Runfa.RF.Shi', 'Shunan.Lu', 'OutSource1', 'OutSource2']
  'WH': ['Hq.Cai', 'Tongchun.Song', 'Fanwei.Xie', 'OutSource1', 'OutSource2']
