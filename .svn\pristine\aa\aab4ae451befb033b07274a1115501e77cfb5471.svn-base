import re
from datetime import datetime, time, timedelta

import dash_ag_grid as dag
import dash_bootstrap_components as dbc
import dash_mantine_components as dmc
import feffery_antd_charts as fact
import feffery_antd_components.alias as fac
import feffery_utils_components.alias as fuc
import numpy as np
import pandas as pd
import plotly.express as px
import plotly.graph_objs as go
import polars as pl
import psycopg2
from dash import ClientsideFunction, clientside_callback, set_props
from dash.exceptions import PreventUpdate
from dash_extensions.enrich import (
    ALL,
    MATCH,
    Input,
    Output,
    State,
    callback,
    ctx,
    dash_table,
    dcc,
    html,
    no_update,
)
from dash_extensions.javascript import Namespace
from dash_snap_grid import Grid
from dash_tabulator import DashTabulator
from plotly.subplots import make_subplots

from ai.smd_plan.main import smd_plan
from common import db_insert, id_factory, read_db, read_sql
from components import dip_card, notice
from config import cfg, pg_conn, pool
from tasks import bg_mail
from utils import IdGenerator, db

from .side_bar import sidebar

id = id_factory(__name__)
ns = Namespace("myNamespace", "tabulator")

style_header = {
    "backgroundColor": "rgba(52, 73, 94)",
    "color": "white",
    "fontWeight": "bold",
    "fontSize": "12px",
    "text-align": "center",
    "textTransform": "uppercase",
}

style_cell = {
    "whiteSpace": "normal",
    "height": "auto",
    "textAlign": "left",
    "font-family": "Helvetica",
    "font-size": "10px",
}

tab1_content = html.Div([html.Br(), html.Div(id="output")])

page2_content = html.Div(
    [
        fac.Space(
            [
                fac.DateRangePicker(id="sm-dpr-2"),
                dbc.Button(
                    "数据逻辑",
                    id="logic-btn",
                    size="lg",
                    color="info",
                    outline=True,
                    class_name="border-0",
                ),
            ],
            size=50,
        ),
        dbc.Popover(
            dcc.Markdown(
                """
                    1. 筛选条件:时间段内样制“首样结束”
                    2. 间隔时长按自然日计算，单位天，保留2位小数
                    3. 准备日期：取到板日期和料齐日期的最大值，都为空时，等于贴片开始时间
                    4. 料齐时间：准备日期-到板日期
                    5. 等待时间：贴片开始时间-准备日期
                    6. 制作时间：首样结束时间-贴片开始时间
                    7. 流程时间：首样结束时间-准备日期
                """
            ),
            target="logic-btn",
            body=True,
            trigger="hover",
            placement="bottom",
        ),
        html.Br(),
        html.Br(),
        html.Div(id="output2"),
    ]
)


def page3_content():
    date = datetime.now().date()
    div = html.Div(
        [
            dcc.DatePickerSingle(
                id="sm-dps-1",
                placeholder="选择生产日期",
                display_format="YYYY-M-D",
                date=date,
            ),
            dcc.Loading(id="my300-output"),
            html.Br(),
            html.Div(id="sm-output"),
        ]
    )
    return div


tab4_content = html.Div([html.Br(), html.Div(id="tab4-output")])

tab5_content = html.Div([html.Br(), html.Div(id="tab5-output")])
tab6_content = dmc.Stack(
    [
        dmc.Group(
            [
                dmc.TextInput(label="输入项目号", id="prtno"),
                dmc.Button("查询", id="smbom-btn"),
            ],
            align="flex-end",
        ),
        DashTabulator(
            data=[],
            id="tab6_table",
            theme="tabulator_site",
            downloadButtonType={
                "css": "btn btn-sm btn-outline-dark",
                "text": "Export",
                "type": "xlsx",
            },
            clearFilterButtonType={
                "css": "btn btn-sm btn-outline-dark",
                "text": "Clear Filters",
            },
            columns=[
                {
                    "title": i,
                    "field": i,
                    "headerFilter": True,
                }
                for i in (
                    "designno",
                    "deltapn",
                    "des",
                    "mfgname",
                    "mfgpn",
                    "packaging",
                    "checkcode",
                    "create_time",
                )
            ],
            options={
                "placeholder": "输入项目号，点击查询",
                "height": "400px",
            },
        ),
    ]
)


def page0_output():
    output = dmc.SimpleGrid(
        [
            dmc.Paper(
                dmc.RingProgress(
                    sections=[{"value": 30, "color": "red"}],
                    label=[dmc.Text("未处理", color="red", align="center")],
                ),
                shadow="xl",
                withBorder=True,
            ),
            dmc.Paper(
                dmc.RingProgress(
                    sections=[{"value": 75, "color": "blue"}],
                    label=[dmc.Text("在途", color="blue", align="center")],
                ),
                shadow="xl",
                withBorder=True,
            ),
            dmc.Paper(
                dmc.RingProgress(
                    sections=[{"value": 100, "color": "green"}],
                    label=[dmc.Text("已处理", color="green", align="center")],
                ),
                shadow="xl",
                withBorder=True,
            ),
        ],
        cols=3,
    )
    return output


def project_review():
    df = read_sql(
        "select a.*,b.area,c.bom_owner,d.area as ee_area from prt_temp a \
        left join dept b on a.dept_id=b.id \
        left join bom_duty c on a.dept_id=c.dept_id \
        left join user d on a.ee=d.nt_name"
    )
    df.columns = df.columns.str.lower()
    df["smd_area"] = "SH"
    df["dip_area"] = df["area"]
    c1 = df["ee_area"] == "WH"
    c2 = df["dept_id"] == 21
    df["dip_area"] = np.where(c1 & c2, "WH", df["dip_area"])
    df["area"] = np.where(c1 & c2, "WH", df["area"])
    date_obj = "d3.timeParse('%Y-%m-%dT%H:%M:%S')(params.data.appdate)"
    table = dag.AgGrid(
        id=id("project-table"),
        className="ag-theme-quartz",
        columnDefs=[
            {"headerName": "id", "field": "id", "hide": True},
            {
                "field": "action",
                "headerName": "ACTION",
                "cellEditor": {"function": "DMC_Select"},
                "cellEditorParams": {
                    "options": ["接收", "删除"],
                    "clearable": True,
                    "shadow": "xl",
                },
                "cellEditorPopup": True,
                "singleClickEdit": True,
                "width": 100,
            },
            {
                "field": "smd_area",
                "headerName": "贴片地",
                "cellEditor": {"function": "DMC_Select"},
                "cellEditorParams": {
                    "options": ["SH", "HZ", "WH", "CQ", "NJ"],
                    # "clearable": True,
                    "shadow": "xl",
                },
                "cellEditorPopup": True,
                "singleClickEdit": True,
                "width": 100,
            },
            {
                "field": "dip_area",
                "headerName": "插件地",
                "cellEditor": {"function": "DMC_Select"},
                "cellEditorParams": {
                    "options": ["SH", "HZ", "WH", "CQ", "NJ"],
                    # "clearable": True,
                    "shadow": "xl",
                },
                "cellEditorPopup": True,
                "singleClickEdit": True,
                "width": 100,
            },
            {
                "field": "bom_owner",
                "headerName": "BOM处理人",
                "width": 130,
            },
            {
                "field": "appdate",
                "headerName": "申请日",
                "valueGetter": {"function": date_obj},
                "valueFormatter": {"function": "d3.timeFormat('%m/%d')(params.value)"},
                "width": 100,
            },
            {"field": "area", "headerName": "区域", "width": 80, "hide": True},
            {
                "field": "dept",
                "headerName": "部门",
                "width": 80,
            },
            {
                "field": "proj",
                "headerName": "机种名",
                "width": 100,
            },
            {
                "field": "qty",
                "headerName": "数量",
                "width": 80,
            },
            {
                "field": "pm",
                "headerName": "PM",
                "width": 100,
            },
            {
                "field": "ee",
                "headerName": "EE",
                "width": 100,
            },
            {
                "field": "me",
                "headerName": "ME",
                "width": 100,
            },
            {
                "field": "mag",
                "headerName": "MAG",
                "width": 100,
            },
            {
                "field": "layout",
                "headerName": "Layout",
                "width": 100,
            },
        ],
        # columnSize="autoSize",
        rowData=df.to_dict(orient="records"),
        defaultColDef={
            "editable": True,
            "resizable": True,
            "sortable": True,
            "filter": True,
        },
        dashGridOptions={
            "rowSelection": "single",
            "stopEditingWhenCellsLoseFocus": True,
            "singleClickEdit": True,
        },
        style={"height": "70vh"},
    )
    layout = dmc.Stack(
        [
            table,
            dmc.Button("接收", id=id("project-submit")),
        ]
    )
    return layout


def project_modify():
    sql = "select id,smstatus,prtno,smd_area,dip_area,qty,bom_owner,\
        area,dept,proj,b_ee,b_me,b_mag from prt \
        where bom_owner is not null \
            and smstatus in (%s,%s) \
            order by id desc"
    df = read_sql(sql, params=["Planning", "PlanOK"])

    df.columns = df.columns.str.lower()
    df["sm_qty"] = df["qty"]
    table = dag.AgGrid(
        id=id("project-modify-table"),
        className="ag-theme-quartz",
        columnDefs=[
            {"headerName": "id", "field": "id", "hide": True},
            # {"headerName": "Menu", "field": "menu", "cellRenderer": "rowMenu"},
            {
                "field": "action",
                "headerName": "ACTION",
                "cellEditor": {"function": "DMC_Select"},
                "cellEditorParams": {
                    "options": ["update"],
                    "clearable": True,
                    "shadow": "xl",
                },
                "cellEditorPopup": True,
                "singleClickEdit": True,
                "width": 100,
                "editable": True,
                "pinned": "left",
            },
            {"field": "smstatus", "headerName": "状态", "width": 100},
            {
                "field": "prtno",
                "headerName": "项目号",
                "width": 130,
            },
            {
                "field": "smd_area",
                "headerName": "贴片地",
                "cellEditor": {"function": "DMC_Select"},
                "cellEditorParams": {
                    "options": ["SH", "HZ", "WH"],
                    "clearable": True,
                    "shadow": "xl",
                },
                "cellEditorPopup": True,
                "singleClickEdit": True,
                "width": 100,
                # "editable": True,
            },
            {
                "field": "dip_area",
                "headerName": "插件地",
                "cellEditor": {"function": "DMC_Select"},
                "cellEditorParams": {
                    "options": ["SH", "HZ", "WH"],
                    "clearable": True,
                    "shadow": "xl",
                },
                "cellEditorPopup": True,
                "singleClickEdit": True,
                "width": 100,
                # "editable": True,
            },
            {
                "field": "qty",
                "headerName": "数量",
                "width": 80,
                "editable": True,
                "cellEditor": "agNumberCellEditor",
            },
            {"field": "sm_qty", "headerName": "样制数量", "width": 80, "hide": True},
            {"field": "bom_owner", "headerName": "BOM处理人", "width": 130},
            {"field": "area", "headerName": "区域", "width": 80},
            {"field": "dept", "headerName": "部门", "width": 100},
            {"field": "proj", "headerName": "机种名", "width": 100},
            {"field": "b_ee", "headerName": "EE", "width": 100},
            {"field": "b_me", "headerName": "ME", "width": 100},
            {"field": "b_mag", "headerName": "MAG", "width": 100},
        ],
        # columnSize="autoSize",
        rowData=df.to_dict(orient="records"),
        defaultColDef={
            "resizable": True,
            "sortable": True,
            "filter": True,
        },
        dashGridOptions={
            "rowSelection": "single",
            "stopEditingWhenCellsLoseFocus": True,
            "singleClickEdit": True,
        },
        style={"height": "70vh"},
    )
    layout = dmc.Stack(
        [
            table,
            dmc.Button("提交", id=id("project-modify-submit")),
        ]
    )
    return layout


def layout(page=None, user=None, **kwargs):
    style = None
    if page == "0":
        content = page0_output()
    elif page == "1":
        content = page1_output()
    elif page == "2":
        content = page2_content
    elif page == "3":
        content = page3_content()
    elif page == "4":
        content = page4_output()
    elif page == "5":
        content = page5_output()
    elif page == "6":
        content = tab6_content
    elif page == "7":
        style = {"position": "fixed"}
        content = project_review()
    elif page == "8":
        style = {"position": "fixed"}
        content = project_modify()
    elif page == "project":
        style = {"position": "fixed"}
        content = project_layout()
    elif page == "smd":
        content = smd_layout(user)
    elif page == "dip":
        style = {"position": "fixed"}
        content = dip_layout(user)
    else:
        content = html.Div("待开发")

    layout = dmc.AppShell(
        html.Div(content, id="content"),
        navbar=sidebar(page),
        style=style,
        id=id("shell"),
    )
    return layout


# ?---------回调函数----------------


def page1_output():
    now = datetime.now()
    sql = (
        'select prtno,CONCAT_WS("_",b.project,a.board) as proj,dept,qty,pcbstatus,mat_ready_date,\
        leadtime,startdate_sch,fsdate_sch,smd_sum,findate_sch from ssp.prt \
        a LEFT JOIN project b on a.project_id=b.id \
        where prtno like %s and smstatus!=%s and startdate_sch>=CURDATE()'
    )
    params = ["SH%", "cancel"]
    df = read_sql(sql, params=params)
    df["proj"] = (
        df["proj"]
        .str.replace("^.+）", "")
        .str.replace("^.+\)", "")
        .str.replace("^.+;", "")
    )

    sql = (
        "select prtno,dept,qty,smtfin_date,startdate_sch,smd_sum,leadtime from ssp.prt \
        where prtno like %s and smstatus!=%s and startdate_sch>=DATE_SUB(CURDATE(), INTERVAL 14 DAY) \
            and startdate_sch<%s"
    )
    params = ["SH%", "cancel", now]  # ,'progok','mat1ok','mat2ok'
    df_last_week = read_sql(sql, params=params)
    df_last_week.columns = [
        "项目号",
        "部门",
        "数量",
        "贴片结束",
        "计划开始",
        "点位数",
        "工时",
    ]
    df_last_week["计划开始"] = df_last_week["计划开始"].dt.date
    df_last_week["贴片结束"] = df_last_week["贴片结束"].dt.date
    df_last_week["工时"] = df_last_week["工时"].astype(int)
    # conn.close()

    df["到板日期"] = pd.to_datetime(df["pcbstatus"], errors="coerce")
    df["料齐日期"] = pd.to_datetime(df["mat_ready_date"], errors="coerce")
    c1 = df["到板日期"].notna()
    c2 = df["料齐日期"].notna()
    df = df.loc[c1 | c2]

    df["准备日期"] = df[["到板日期", "料齐日期"]].max(axis=1)
    c1 = df["到板日期"].isna()
    c2 = df["料齐日期"].isna()
    df["准备日期"] = np.where(c1 | c2, None, df["准备日期"])
    df["准备日期"] = pd.to_datetime(df["准备日期"], errors="coerce")
    df["准备日期"] = df["准备日期"] + timedelta(days=1)

    c1 = df["到板日期"].notna()
    c2 = df["料齐日期"].notna()
    df["类型"] = np.where(c1, "板齐", "板缺")
    df["类型"] = np.where(c2, df["类型"] + "料齐", df["类型"] + "料缺")

    # df['状态']=np.where(df['类型']=='板齐料齐','待上线',None)
    # df['状态']=np.where(df['类型']=='板齐料缺','待材料',df['状态'])
    # df['状态']=np.where(df['类型']=='板缺料齐','待PCB',df['状态'])

    df["点位数"] = df["smd_sum"] * df["qty"]
    df["计划开始"] = pd.to_datetime(df["startdate_sch"], errors="coerce").dt.date

    df["等待时长"] = now - df["准备日期"]
    df["等待时长"] = df["等待时长"].dt.days
    df["等待时长"] = np.where(df["等待时长"] < 0, 0, df["等待时长"])

    df = df[
        [
            "prtno",
            "proj",
            "dept",
            "qty",
            "leadtime",
            "点位数",
            "到板日期",
            "料齐日期",
            "准备日期",
            "计划开始",
            "等待时长",
            "类型",
            "findate_sch",
        ]
    ]
    df = df.rename(
        columns={
            "prtno": "项目号",
            "proj": "机种名",
            "dept": "部门",
            "qty": "数量",
            "leadtime": "工时",
            "findate_sch": "备注",
        }
    )

    df3 = df.loc[df["计划开始"] <= (now + pd.DateOffset(months=1))]
    df3 = df3.groupby(["类型"], as_index=False).agg({"项目号": "count"})
    df3 = df3.rename(columns={"项目号": "项目数"})

    x = df3["项目数"]
    y = df3["类型"]
    w = [0.3] * df3.shape[0]
    fig = go.Figure(data=[go.Bar(x=x, y=y, width=w, orientation="h")])
    fig.update_layout(title_text="未来样制需求(1月)")
    graph = dcc.Graph(figure=fig, config={"displayModeBar": False})

    output2 = dbc.Row(
        [
            dbc.Col(graph, width=6, align="start"),
            dbc.Col(
                create_table(df3), width=6, align="start", style={"margin-top": 70}
            ),
        ]
    )

    c1 = df["等待时长"] >= 3
    c2 = df["等待时长"] <= 7
    c3 = df["等待时长"] > 7
    df["等待状态"] = np.where(c1 & c2, "3至7天", None)
    df["等待状态"] = np.where(c3, "大于7天", df["等待状态"])
    df1 = df.loc[df["等待状态"].notna()]
    df11 = pd.pivot_table(
        df1,
        index="部门",
        columns="等待状态",
        values="项目号",
        aggfunc="count",
        fill_value=0,
    )
    df11 = df11.reset_index()
    df11 = df11.reindex(columns=["部门", "3至7天", "大于7天"])
    fig = go.Figure()
    fig.add_trace(
        go.Bar(
            x=df11["部门"], y=df11["3至7天"], name="3至7天", width=[0.2] * df11.shape[0]
        )
    )
    fig.add_trace(
        go.Bar(
            x=df11["部门"],
            y=df11["大于7天"],
            name="大于7天",
            width=[0.2] * df11.shape[0],
        )
    )
    fig.update_layout(title_text="等待时长异常统计")

    graph1 = dcc.Graph(figure=fig, config={"displayModeBar": False})
    df1 = df1.reindex(columns=["部门", "机种名", "等待状态", "备注"])

    output3 = dbc.Row(
        [
            dbc.Col(graph1, width=6, align="start"),
            dbc.Col(
                create_table(df11), width=2, align="start", style={"margin-top": 70}
            ),
            dbc.Col(
                create_table(df1), width=4, align="start", style={"margin-top": 70}
            ),
        ]
    )

    df4 = df_last_week.groupby("计划开始", as_index=False).agg(
        {"工时": "sum", "项目号": "count"}
    )
    df5 = df_last_week.groupby("贴片结束", as_index=False).agg(
        {"工时": "sum", "项目号": "count"}
    )

    fig = make_subplots(specs=[[{"secondary_y": True}]])
    fig.add_trace(
        go.Bar(x=df4["计划开始"], y=df4["项目号"], name="计划项目数"),
        secondary_y=False,
    )
    fig.add_trace(
        go.Bar(x=df5["贴片结束"], y=df5["项目号"], name="实际项目数"),
        secondary_y=False,
    )
    fig.add_trace(
        go.Scatter(x=df5["贴片结束"], y=df5["工时"], name="工时"),
        secondary_y=True,
    )
    fig.update_layout(
        title_text="产能计划与达成(过去2周)",
        # yaxis=dict(
        #     tickvals  = tuple(range(16))
        # ),
        # yaxis2=dict(
        #     tickvals  = tuple(range(31))
        # )
    )

    graph = dcc.Graph(figure=fig, config={"displayModeBar": False})

    output4 = dbc.Row(
        [
            dbc.Col(graph, width=8, align="start"),
            dbc.Col(
                create_table(df4), width=2, align="start", style={"margin-top": 70}
            ),
            dbc.Col(
                create_table(df5), width=2, align="start", style={"margin-top": 70}
            ),
        ]
    )

    df5 = df.groupby("计划开始", as_index=False).agg({"工时": "sum", "项目号": "count"})
    df5 = df5.loc[df5["计划开始"] <= (now.date() + timedelta(days=7))]
    df5["工时"] = df5["工时"].astype(int)
    # data1={'x':df5['计划开始'],'y':df5['项目号'],'type': 'bar', 'name': '项目数'}
    # data2={'x':df5['计划开始'],'y':df5['工时'],'type': 'line', 'name': '工时'}
    # figure={'data':[data1,data2],'layout':{'title': '计划(未来2周)'}}
    # graph5=dcc.Graph(figure=figure,config={'displayModeBar': False})
    fig = make_subplots(specs=[[{"secondary_y": True}]])
    x = pd.to_datetime(df5["计划开始"]).dt.strftime("%m/%d").tolist()
    y = df5["项目号"].tolist()

    fig.add_trace(
        go.Bar(
            x=x, y=df5["项目号"], name="项目数", yaxis="y1", width=[0.2] * df5.shape[0]
        ),
        secondary_y=False,
    )

    fig.add_trace(
        go.Scatter(x=x, y=df5["工时"], name="工时", yaxis="y2"),
        secondary_y=True,
    )

    # fig = go.Figure(data=[go.Bar(
    #     x=[1, 2, 3, 5.5, 10],
    #     y=[10, 8, 6, 4, 2],
    #     width=[0.8, 0.8, 0.8, 3.5, 4] # customize width here
    # )])
    fig.update_layout(
        # autosize=False,
        # width=500,
        # margin=dict(l=20, r=20, t=20, b=20),
        title_text="产能计划（未来1周）",
        # yaxis=dict(
        #     tickvals  = tuple(range(16))
        # ),
        # yaxis2=dict(
        #     tickvals  = tuple(range(31))
        # )
    )
    graph5 = dcc.Graph(figure=fig, config={"displayModeBar": False})

    output5 = dbc.Row(
        [
            dbc.Col(graph5, width=8, align="start"),
            dbc.Col(
                create_table(df5), width=4, align="start", style={"margin-top": 70}
            ),
        ]
    )

    df[["到板日期", "料齐日期", "准备日期"]] = df[
        ["到板日期", "料齐日期", "准备日期"]
    ].apply(lambda x: x.dt.date)

    df = df.sort_values(by=["计划开始", "准备日期"])
    table = dash_table.DataTable(
        data=df.to_dict(orient="records"),
        columns=[{"name": ["未来样制计划", i], "id": i} for i in df.columns],
        id="sm-stat-table1",
        style_header=style_header,
        merge_duplicate_headers=True,
        style_cell=style_cell,
        export_format="xlsx",
        page_action="native",
        page_size=20,
        style_cell_conditional=[
            {"if": {"column_id": "备注"}, "width": "100px"},
            {"if": {"column_id": "机种名"}, "width": "100px"},
        ],
    )

    # df1['startdate_ready']=df1['startdate_ready'].dt.date
    # df1['leadtime']=df1['leadtime'].round(decimals=1)
    # df1=df1.rename(columns={'startdate_ready':'准备日期','leadtime':'工时','prtno':'项目数'})
    # table2=dash_table.DataTable(
    #     data=df1.to_dict(orient='records'),
    #     columns=[{"name": i, "id": i} for i in df1.columns],
    #     id='sm-stat-table2',
    #     style_header=style_header,
    #     style_cell=style_cell,
    #     )

    output = html.Div(
        [
            table,
            html.Br(),
            html.Hr(),
            output2,
            html.Hr(),
            output3,
            html.Hr(),
            output4,
            html.Hr(),
            output5,
        ]
    )

    return output


@callback(
    Output("output2", "children"),
    Input("sm-dpr-2", "value"),
)
def tab2_output(value):
    if not value:
        raise PreventUpdate
    start_date, end_date = value
    start_date = f"{start_date} 00:00:00"
    end_date = f"{end_date} 23:59:59"

    sql = "select prtno,dept,proj,qty,placer,leadtime,pcbstatus,mat_ready_date,\
        smtstadate,smtfin_date,dipstadate,fsdate_act,findate_act,smd_sum,smd_count,\
        dip_sum,dip_count,findate_sch as remark,dip_mat_ready from ssp.prt \
        where fsdate_act>=%s and fsdate_act<=%s"

    params = [start_date, end_date]
    df = read_sql(sql, params=params)

    c1 = df["smtstadate"].isna()
    c2 = df["dipstadate"].notna()
    df["smtstadate"] = np.where(c1 & c2, df["dipstadate"], df["smtstadate"])
    # df['smtstadate']=np.where(df['smtstadate'].isna(),df['fsdate_act'],df['smtstadate'])

    c1 = df["fsdate_act"].isna()
    c2 = df["findate_act"].notna()
    df["fsdate_act"] = np.where(c1 & c2, df["findate_act"], df["fsdate_act"])

    # * mat_ready_date贴片料齐时间
    df["pcbstatus"] = pd.to_datetime(df["pcbstatus"], errors="coerce")  # .dt.date
    df["mat_ready_date"] = pd.to_datetime(df["mat_ready_date"], errors="coerce")
    df["smtfin_date"] = pd.to_datetime(df["smtfin_date"], errors="coerce")  # .dt.date
    df["smtstadate"] = pd.to_datetime(df["smtstadate"], errors="coerce")  # .dt.date
    df["fsdate_act"] = pd.to_datetime(df["fsdate_act"], errors="coerce")  # .dt.date
    df["findate_act"] = pd.to_datetime(df["findate_act"], errors="coerce")
    df["dipstadate"] = pd.to_datetime(df["dipstadate"], errors="coerce")
    df["dip_mat_ready"] = pd.to_datetime(df["dip_mat_ready"], errors="coerce")

    # * 有无贴片
    c1 = df["smd_sum"] != 0
    df["mat_ready_date"] = np.where(c1, df["mat_ready_date"], df["dip_mat_ready"])
    df["startdate_ready"] = df[["pcbstatus", "mat_ready_date"]].max(axis=1)

    c1 = df["pcbstatus"].isna()
    c2 = df["mat_ready_date"].isna()
    # *pcbstatus和mat_ready_date都为空,等于smtstadate
    df["startdate_ready"] = np.where(c1 & c2, df["smtstadate"], df["startdate_ready"])
    df["startdate_ready"] = pd.to_datetime(df["startdate_ready"], errors="coerce")
    df["SMT时间"] = df["smtfin_date"] - df["smtstadate"]
    df["贴片总数"] = df["smd_sum"] * df["qty"]
    df["插件总数"] = df["dip_sum"] * df["qty"]
    df["startdate_ready"] = df["startdate_ready"].dt.normalize() + pd.Timedelta(
        "23:59:59"
    )

    for i in df.itertuples():
        ts = (i.smtstadate - i.startdate_ready).total_seconds()
        df.loc[i.Index, "等待时间"] = round((ts - (24 * 60 * 60)) / (24 * 60 * 60), 2)

        ts = (i.fsdate_act - i.smtstadate).total_seconds()
        df.loc[i.Index, "制作时间"] = round(ts / (24 * 60 * 60), 2)

        ts = (i.fsdate_act - i.startdate_ready).total_seconds()
        df.loc[i.Index, "流程时间"] = round((ts - (24 * 60 * 60)) / (24 * 60 * 60), 2)

    df["等待时间"] = np.where(df["等待时间"] < 0, 0, df["等待时间"])
    df["制作时间"] = np.where(df["制作时间"] < 0, 0, df["制作时间"])
    df["流程时间"] = np.where(df["流程时间"] < 0, 0, df["流程时间"])
    df["首样插件时间"] = round(
        (df["fsdate_act"] - df["dipstadate"]).dt.total_seconds() / (24 * 60 * 60), 2
    )

    df["SMT时间"] = (df["SMT时间"].dt.total_seconds() / 3600).round(decimals=1)
    df["SMT时间"] = np.where(
        df["SMT时间"] > df["leadtime"], df["leadtime"], df["SMT时间"]
    )

    df["料齐时间"] = (
        df[["dip_mat_ready", "mat_ready_date"]].max(axis=1) - df["pcbstatus"]
    )

    # df["料齐时间"] = np.where(
    #     df["mat_ready_date"].isna(),
    #     df["startdate_ready"] - df["pcbstatus"],
    #     df["料齐时间"],
    # )

    df["料齐时间"] = round(df["料齐时间"].dt.total_seconds() / (24 * 60 * 60), 2)
    df["料齐时间"] = np.where(df["pcbstatus"].isna(), "缺数据", df["料齐时间"])

    df["pcbstatus"] = df["pcbstatus"].dt.strftime("%Y/%m/%d %H:%M:%S")
    df["mat_ready_date"] = df["mat_ready_date"].dt.strftime("%Y/%m/%d %H:%M:%S")
    df["smtstadate"] = df["smtstadate"].dt.strftime("%Y/%m/%d %H:%M:%S")
    df["smtfin_date"] = df["smtfin_date"].dt.strftime("%Y/%m/%d %H:%M:%S")
    df["fsdate_act"] = df["fsdate_act"].dt.strftime("%Y/%m/%d %H:%M:%S")
    df["dipstadate"] = df["dipstadate"].dt.strftime("%Y/%m/%d %H:%M:%S")
    df["findate_act"] = df["findate_act"].dt.strftime("%Y/%m/%d %H:%M:%S")
    df["startdate_ready"] = df["startdate_ready"].dt.strftime("%Y/%m/%d %H:%M:%S")

    df = df.rename(
        columns={
            "pcbstatus": "到板日期",
            "mat_ready_date": "料齐日期",
            "startdate_ready": "准备日期",
        }
    )

    col1 = [
        "prtno",
        "dept",
        "proj",
        "qty",
        "到板日期",
        "料齐日期",
        "dip_mat_ready",
        "准备日期",
        "料齐时间",
        "等待时间",
        "制作时间",
        "流程时间",
        "首样插件时间",
        "smtstadate",
        "smtfin_date",
        "dipstadate",
        "fsdate_act",
        "findate_act",
        "placer",
        "leadtime",
        "SMT时间",
        "贴片总数",
        "插件总数",
    ]

    col2 = df.columns.difference(col1).tolist()
    df = df.reindex(columns=col1 + col2)
    df = df.rename(
        columns={
            "prtno": "项目号",
            "dept": "部门",
            "proj": "机种名",
            "qty": "数量",
            "placer": "贴片机",
            "leadtime": "工时",
            "smtstadate": "贴片开始",
            "smtfin_date": "贴片结束",
            "dipstadate": "插件开始",
            "fsdate_act": "首样完成",
            "findate_act": "项目结束",
            "dip_mat_ready": "插件料齐",
            "料齐日期": "贴片料齐",
        }
    )

    sql = "select CONCAT_WS('_',dept_group,dept_name) AS dept,category from ssp.dept"
    depts = read_sql(sql)

    sh_depts = depts.query("category=='SDC'")["dept"]
    hz_depts = depts.query("category=='HDC'")["dept"]

    df["分类"] = np.where(df["部门"].isin(sh_depts), "SDC", None)
    df["分类"] = np.where(df["部门"].isin(hz_depts), "HDC", df["分类"])
    df["分类"] = np.where(df["分类"].isna(), "OTHERS", df["分类"])
    df["点位数"] = df["贴片总数"] + df["插件总数"]

    df1 = df.groupby(["分类", "部门"], as_index=False).agg(
        {
            "等待时间": np.mean,
            "制作时间": np.mean,
            "流程时间": np.mean,
            "项目号": "count",
            "点位数": "sum",
        }
    )
    df1["等待时间"] = df1["等待时间"].round(decimals=1)
    df1["制作时间"] = df1["制作时间"].round(decimals=1)
    df1["流程时间"] = df1["流程时间"].round(decimals=1)
    df1 = df1.rename(columns={"项目号": "项目数"})

    df1["分类"] = df1["分类"].astype("category")
    df1["分类"] = df1["分类"].cat.set_categories(["SDC", "HDC", "OTHERS"])
    df1 = df1.sort_values(by="分类")

    sql = (
        "select prtno as 项目号,placer as 贴片机,qty as 样制数量,smd_sum*qty as 点位数,\
        leadtime,smtstadate,smtfin_date from ssp.prt \
        where smtfin_date>=%s and smtfin_date<=%s"
    )
    params = [start_date, end_date]
    df2 = read_sql(sql, params=params)
    # conn.close()

    df2["SMT时间"] = df2["smtfin_date"] - df2["smtstadate"]
    df2["SMT时间"] = (df2["SMT时间"].dt.total_seconds() / 3600).round(decimals=1)
    df2["SMT时间"] = np.where(
        df2["SMT时间"] > df2["leadtime"], df2["leadtime"], df2["SMT时间"]
    )

    df2 = df2.groupby("贴片机", as_index=False).agg(
        {"点位数": "sum", "样制数量": "sum", "项目号": "count", "SMT时间": "sum"}
    )
    df2 = df2.rename(columns={"项目号": "项目数"})
    df2 = df2.loc[~df2["贴片机"].isin(["FLX2010"])]
    df2["SMT时间"] = df2["SMT时间"].round(decimals=1)
    smd_std = pd.date_range(start_date, end_date)
    smd_std = smd_std[~smd_std.dayofweek.isin([6])].size
    df2["理论时间"] = smd_std * 8
    df2["产能利用率"] = df2["SMT时间"] / df2["理论时间"]
    df2["产能利用率"] = df2["产能利用率"].map(lambda x: f"{x * 100:.0f}%")

    table = dash_table.DataTable(
        data=df.to_dict(orient="records"),
        columns=[{"name": i, "id": i} for i in df.columns],
        id="sm-stat-table",
        style_header=style_header,
        style_cell=style_cell,
        style_table={"overflowX": "auto"},
        export_format="xlsx",
        page_action="native",
        page_size=10,
        filter_action="native",
    )

    output = html.Div(
        [
            table,
            html.Br(),
            html.Br(),
            html.Hr(),
            dbc.Row(
                [
                    dbc.Col(
                        [
                            html.H3("流程时间统计"),
                            create_table(df1),
                        ]
                    ),
                    dbc.Col(
                        [
                            html.H3("SMT产能利用率统计"),
                            create_table(df2),
                        ]
                    ),
                ]
            ),
        ]
    )

    return output


# @callback(Output("ws", "send"), [Input("sm-dps-1", "date")])
# def send(date):
#     return date


# @callback(Output("my300-output", "children"), [Input("ws", "message")])
# def message(e):
#     if e is None:
#         raise PreventUpdate

#     df = pd.read_json(e["data"])
#     if df.empty:
#         raise PreventUpdate

#     fig = px.timeline(
#         df, x_start="timestamp_start", x_end="timestamp_stop", y="prtno", color="prtno"
#     )
#     g2 = dcc.Graph(figure=fig, config={"displayModeBar": False})
#     return g2


@callback(
    Output("my300-output", "children"),
    Input("sm-dps-1", "date"),
    prevent_initial_call=False,
)
def my300_output_graph(date):
    start_date = datetime.strptime(f"{date} 00:00:00", "%Y-%m-%d %H:%M:%S")
    end_date = datetime.strptime(f"{date} 23:59:59", "%Y-%m-%d %H:%M:%S")
    pg_conn["dbname"] = "eventdb"
    conn = psycopg2.connect(**pg_conn)

    sql = "SELECT b.name as prtno,a.timestamp_start,a.timestamp_stop \
        FROM data.assembly a \
        LEFT JOIN data.layout b on a.layout_id=b.id \
        where a.timestamp_start>%s and a.timestamp_stop<%s"
    df = pd.read_sql(sql, conn, params=[start_date, end_date])

    if df.empty:
        raise PreventUpdate

    df = df.reindex(columns=["prtno", "timestamp_start", "timestamp_stop"])
    df["timestamp_start"] = df["timestamp_start"] + pd.offsets.Hour(8)
    df["timestamp_stop"] = df["timestamp_stop"] + pd.offsets.Hour(8)
    df = df.sort_values(by="timestamp_start")
    dfh = df.head(1)
    start = (dfh["timestamp_start"].iloc[0]).time()
    if start > time(8, 45):
        dfh["prtno"] = "8点45分"
        dfh["timestamp_start"] = dfh["timestamp_start"].dt.normalize() + pd.Timedelta(
            hours=8, minutes=45
        )
        dfh["timestamp_stop"] = dfh["timestamp_stop"].dt.normalize() + pd.Timedelta(
            hours=8, minutes=46
        )
        df = pd.concat([dfh, df])

    df["timestamp_start_shift"] = df["timestamp_start"].shift(-1)

    df["timestamp_start_shift"] = pd.to_datetime(df["timestamp_start_shift"])
    df["timestamp_stop"] = pd.to_datetime(df["timestamp_stop"])

    df["timestamp_mid"] = (
        df["timestamp_start_shift"]
        + (df["timestamp_stop"] - df["timestamp_start_shift"]) / 2
    )
    df["stop"] = df["timestamp_start_shift"] - df["timestamp_stop"]
    df["stop"] = df["stop"].dt.seconds / 60
    df["stop"] = df["stop"].fillna(0).astype(int)

    fig = make_subplots(specs=[[{"secondary_y": True}]])

    fig1 = px.timeline(
        df, x_start="timestamp_start", x_end="timestamp_stop", y="prtno", color="prtno"
    )
    fig.layout.xaxis = fig1.layout.xaxis
    for trace in fig1.data:
        trace.width = 1
        fig.add_trace(trace, secondary_y=False)

    fig.add_trace(
        go.Scatter(
            x=df["timestamp_mid"],
            y=df["stop"],
            # text=df["stop"].tolist(),
            # textposition="top center",
            name="停机时间",
        ),
        secondary_y=True,
    )
    df["next_prtno"] = df["prtno"].shift(-1)
    c1 = df["stop"] > 30
    c2 = df["prtno"] != df["next_prtno"]
    dfx1 = df.loc[c1 & c2]

    c1 = df["stop"] > 15
    c2 = df["prtno"] == df["next_prtno"]
    dfx2 = df.loc[c1 & c2]
    dfx = []
    if not dfx1.empty:
        dfx1["type"] = "切线效率"
        dfx.append(dfx1)
        for i in dfx1.itertuples():
            fig.add_annotation(
                x=i.timestamp_mid,
                y=i.stop,
                text="切线效率",
                showarrow=True,
                arrowhead=1,
                font=dict(color="red"),
                yref="y2",
            )
    if not dfx2.empty:
        dfx2["type"] = "贴装效率"
        dfx.append(dfx2)
        for i in dfx2.itertuples():
            fig.add_annotation(
                x=i.timestamp_mid,
                y=i.stop,
                text="贴装效率",
                showarrow=True,
                arrowhead=1,
                font=dict(color="orange"),
                yref="y2",
            )
    table = html.Div()
    if dfx:
        dfx = pd.concat(dfx)
        dfx = dfx.reindex(
            columns=[
                "type",
                "stop",
                "timestamp_stop",
                "timestamp_start_shift",
                "reason",
                "处理方式",
                "职责人",
            ]
        )
        dfx["timestamp_start_shift"] = dfx["timestamp_start_shift"].dt.strftime(
            "%Y-%m-%d %H:%M:%S"
        )
        dfx["timestamp_stop"] = dfx["timestamp_stop"].dt.strftime("%Y-%m-%d %H:%M:%S")
        dfx = dfx.rename(
            columns={
                "timestamp_start_shift": "结束",
                "timestamp_stop": "开始",
                "stop": "分钟",
                "reason": "原因",
                "type": "类型",
            }
        )
        table = dag.AgGrid(
            className="ag-theme-quartz",
            columnDefs=[{"field": i, "headerName": i} for i in dfx.columns],
            columnSize="sizeToFit",
            rowData=dfx.to_dict(orient="records"),
            defaultColDef={
                "editable": True,
                "resizable": True,
                "sortable": True,
                "filter": True,
            },
            dashGridOptions={
                "rowSelection": "single",
                "stopEditingWhenCellsLoseFocus": True,
                "singleClickEdit": True,
            },
        )

    fig.update_yaxes(
        # tickmode="array",
        tickvals=[15, 30, 45, 60, 75, 90, 105, 120],
        ticktext=["15", "30", "45", "60", "75", "90", "105", "120"],
        secondary_y=True,
        range=[0, 120],
    )

    fig.update_traces(showlegend=False)

    g2 = dcc.Graph(figure=fig, config={"displayModeBar": False})
    div = html.Div([g2, table])
    return div


@callback(Output("sm-output", "children"), Input("sm-dps-1", "date"))
def sm_output(date):
    sql = (
        "select dept,prtno,proj,smtstadate as smd_start,smtfin_date as smd_end,dipstadate as dip_start,\
        fsdate_act as dip_end,leadtime,placer,dip_std_time from ssp.prt \
        where date(smtfin_date)=%s or date(fsdate_act)=%s"
    )
    params = [date, date]
    prt = read_sql(sql, params=params)
    prt["dip_end"] = pd.to_datetime(prt["dip_end"], errors="coerce")

    prt["smd_start"] = np.where(
        prt["smd_start"].isna(), prt["smd_end"], prt["smd_start"]
    )

    prt["dip_start"] = np.where(
        prt["dip_start"].isna(), prt["dip_end"], prt["dip_start"]
    )

    smd = prt.loc[prt["smd_end"].dt.date.astype(str) == date]
    dip = prt.loc[prt["dip_end"].dt.date.astype(str) == date]

    for i in smd.itertuples():
        ts = pd.date_range(i.smd_start, i.smd_end, freq="t").to_series()
        ts = ts.loc[ts.dt.dayofweek < 6]
        x1 = ts.between_time("08:45", "11:45").size
        x2 = ts.between_time("13:05", "16:30").size
        smd.loc[i.Index, "smd_actual"] = x1 + x2

    smd["smd_actual"] = (smd["smd_actual"] / 60).round(1)
    smd["smd_rate"] = (smd["leadtime"] - smd["smd_actual"]) / smd["leadtime"]
    smd["smd_rate"] = smd["smd_rate"].round(1)
    smd["text"] = (
        "部门:"
        + smd["dept"]
        + "机种:"
        + smd["proj"]
        + "开始:"
        + smd["smd_start"].astype(str)
        + "结束:"
        + smd["smd_end"].astype(str)
        + "理论工时:"
        + smd["leadtime"].astype(str)
    )

    for i in dip.itertuples():
        ts = pd.date_range(i.dip_start, i.dip_end, freq="t").to_series()
        ts = ts.loc[ts.dt.dayofweek < 6]
        x1 = ts.between_time("08:45", "11:45").size
        x2 = ts.between_time("13:05", "16:30").size
        dip.loc[i.Index, "dip_actual"] = x1 + x2

    dip["dip_actual"] = (dip["dip_actual"] / 60).round(1)
    dip["dip_rate"] = (dip["dip_std_time"] - dip["dip_actual"]) / dip["dip_std_time"]
    dip["dip_rate"] = dip["dip_rate"].round(1)
    dip["text"] = (
        "部门:"
        + dip["dept"]
        + "机种:"
        + dip["proj"]
        + "开始:"
        + dip["dip_start"].astype(str)
        + "结束:"
        + dip["dip_end"].astype(str)
        + "理论工时:"
        + dip["dip_std_time"].astype(str)
        + "实际工时:"
        + dip["dip_actual"].astype(str)
    )

    fig = px.line(smd, x="prtno", y="smd_rate", hover_name="text", markers=True)
    fig.update_layout(
        yaxis=dict(
            tickvals=np.arange(-2, 2, 0.1),
            tickformat=".0%",
        ),
        title_text="贴片工时:(理论-实际)/理论",
        height=400,
    )

    g1 = dcc.Graph(figure=fig, config={"displayModeBar": False})

    fig = px.line(dip, x="prtno", y="dip_rate", hover_name="text", markers=True)
    fig.update_layout(
        yaxis=dict(
            tickvals=np.arange(-2, 2, 0.1),
            tickformat=".0%",
        ),
        title_text="插件工时:(理论-实际)/理论",
        height=400,
    )
    g2 = dcc.Graph(figure=fig, config={"displayModeBar": False})
    return html.Div([g1, g2])


def create_table(df):
    table = dash_table.DataTable(
        data=df.to_dict(orient="records"),
        columns=[{"name": i, "id": i} for i in df.columns],
        hidden_columns=["等待状态"],
        style_header=style_header,
        style_cell=style_cell,
        export_format="xlsx",
        style_data_conditional=[
            {
                "if": {"filter_query": "{等待状态} = 3至7天"},
                "backgroundColor": "#2980b9",
            },
            {
                "if": {"filter_query": "{等待状态} = 大于7天"},
                "backgroundColor": "#c0392b",
            },
        ],
        css=[{"selector": ".dash-spreadsheet-menu-item", "rule": "display:none"}],
    )
    return table


def page4_output():
    sql = 'select b.project,concat("S",a.stage) as stage,a.prtno,a.pcbstatus,\
        a.board,a.pcbpn,a.qty,a.smstatus,a.b_ee,a.b_me,a.b_mag,a.mat_ready_date,\
        a.smtstadate,a.dipstadate,a.startdate_sch,a.fsdate_sch,a.findate_sch,a.dept from ssp.prt a \
        left join ssp.project b on a.project_id=b.id \
        where  (a.smstatus!=%s and a.smstatus!=%s) order by b.gmt_create desc'
    params = ["cancel", "close"]
    prt = read_sql(sql, params=params)

    if prt.empty:
        raise PreventUpdate

    params = prt["prtno"].unique().tolist()
    ph = ",".join(["%s"] * len(params))
    sql = f"select prtno,deltapn,des,es_date,mat_receiveddate,pur_remark from ssp.pur \
        where prtno in ({ph}) and application=%s and pur_status=%s"
    pur = read_sql(sql, params=params + ["project", "wait for material"])

    prt["shortage"] = np.where(prt["prtno"].isin(pur["prtno"]), "缺料", "不缺料")
    c1 = prt["shortage"] == "不缺料"
    c2 = ~(prt[["b_ee", "b_me", "b_mag"]] == "X").any(1)
    prt["shortage"] = np.where(c1 & c2, "未传BOM", prt["shortage"])

    prt["b_ee"] = (
        prt["b_ee"]
        .replace("Y", "未提供")
        .replace("NA", "不需要")
        .replace("X", "已提供")
    )
    prt["b_me"] = (
        prt["b_me"]
        .replace("Y", "未提供")
        .replace("NA", "不需要")
        .replace("X", "已提供")
    )
    prt["b_mag"] = (
        prt["b_mag"]
        .replace("Y", "未提供")
        .replace("NA", "不需要")
        .replace("X", "已提供")
    )
    prt["bom_status"] = (
        "电子:" + prt["b_ee"] + "，机构:" + prt["b_me"] + "，磁:" + prt["b_mag"]
    )

    now = datetime.now()

    prt["pcbstatus"] = np.where(
        prt["pcbstatus"].isin(["库存板", "已提供"]), now, prt["pcbstatus"]
    )
    prt["pcbstatus"] = pd.to_datetime(prt["pcbstatus"], errors="coerce")

    c1 = (prt["pcbstatus"] + pd.offsets.BDay(3)) < now
    c2 = prt["smstatus"].isin(["Planning"])
    df1 = prt.loc[c1 & c2]

    prt["ready_date"] = prt[["pcbstatus", "mat_ready_date"]].max(axis=1)
    c1 = prt["startdate_sch"] > prt["ready_date"] + pd.offsets.BDay(3)
    c2 = prt["smstatus"].isin(["PlanOK"])
    df2 = prt.loc[c1 & c2]

    c1 = (prt["smtstadate"] + pd.offsets.BDay(1)) < now
    c2 = prt["smstatus"].isin(["SMTStart"])
    df3 = prt.loc[c1 & c2]

    c1 = (prt["dipstadate"] + pd.offsets.BDay(3)) < now
    c2 = prt["smstatus"].isin(["DIPStart"])
    df4 = prt.loc[c1 & c2]

    df5 = prt.merge(pur, on="prtno", how="left")
    c1 = df5["pcbstatus"] + pd.offsets.BDay(3) < df5["es_date"]
    c2 = df5["pcbstatus"] <= now
    df5 = df5.loc[c1 & c2]
    df5["smstatus"] = "到板后3天料未齐----条件：缺料状态-PCB状态(<=Now)>3个工作天"
    df5["shortage"] = df5["es_date"].dt.date
    df5["board"] = df5["deltapn"]
    df5["pcbpn"] = df5["des"]
    df5["findate_sch"] = df5["pur_remark"]

    prt = pd.concat([df1, df2, df3, df4, df5])
    prt["pcbstatus"] = prt["pcbstatus"].dt.date
    prt["startdate_sch"] = prt["startdate_sch"].dt.date

    prt["fsdate_sch"] = pd.to_datetime(prt["fsdate_sch"], errors="ignore")

    prt["smstatus"] = (
        prt["smstatus"]
        .replace("Planning", "待排定")
        .replace("PlanOK", "已排定")
        .replace("ProgOK", "已排定")
        .replace("SMTStart", "贴片中")
        .replace("SMTFinish", "插件中")
        .replace("FSFINISH", "首样完成")
        .replace("SMFINISH", "样制完成")
    )

    output1 = DashTabulator(
        data=prt.to_dict(orient="records"),
        theme="tabulator_site",
        options={
            "groupBy": ["smstatus"],
            "height": "400px",
            "clipboard": "copy",
            "groupHeader": ns("group_header"),
            "groupStartOpen": False,
            "selectable": True,
            "selectableRangeMode": "click",
        },
        downloadButtonType={
            "css": "btn btn-secondary",
            "text": "Export",
            "type": "xlsx",
        },
        columns=[
            {"title": "项目名称", "field": "project", "visible": False},
            {"title": "阶段", "field": "stage", "visible": False},
            {"title": "项目号", "field": "prtno", "width": 110},
            {"title": "PCB名称", "field": "board"},
            {"title": "PCB料号", "field": "pcbpn"},
            {"title": "样制数量", "field": "qty", "width": 90},
            {"title": "PCB状态", "field": "pcbstatus", "width": 90},
            {"title": "料表状态", "field": "bom_status", "width": 100},
            {"title": "缺料状态", "field": "shortage", "width": 130},
            {"title": "上线日期", "field": "startdate_sch", "width": 90},
            {"title": "备注", "field": "findate_sch", "width": 200},
            {"title": "样制状态", "field": "smstatus", "width": 90},
            {"title": "部门", "field": "dept", "width": 90},
        ],
    )

    # ?---------------------------

    sql = (
        "select prtno,smtstadate as smd_start,smtfin_date as smd_end,dipstadate as dip_start,\
        fsdate_act as dip_end,leadtime,placer,smd_count,smd_sum,qty,side,panel from ssp.prt \
        where smtfin_date>DATE_SUB(CURDATE(), INTERVAL 1 month)"
    )

    prt = read_sql(sql)

    # prt["smd_start_std"] = prt["smd_start"]
    # prt["smd_end_std"] = prt["smd_start"] + pd.to_timedelta(
    #     prt["leadtime"], unit="hour"
    # )
    # c1 = prt["smd_end_std"].dt.time > pd.to_datetime("16:30:00").time()
    # prt["smd_end_std"] = np.where(
    #     c1, prt["smd_end_std"] + pd.offsets.Hour(16), prt["smd_end_std"]
    # )
    prt["smd_start"] = np.where(
        prt["smd_start"].isna(), prt["smd_end"], prt["smd_start"]
    )

    for i in prt.itertuples():
        ts = pd.date_range(i.smd_start, i.smd_end, freq="t").to_series()
        ts = ts.loc[ts.dt.dayofweek < 6]
        x1 = ts.between_time("08:45", "11:45").size
        x2 = ts.between_time("13:05", "16:30").size
        prt.loc[i.Index, "smd_actual"] = x1 + x2

    prt["leadtime"] = prt["leadtime"] * 60
    prt["smd_rate"] = (prt["leadtime"] - prt["smd_actual"]) / prt["leadtime"]
    prt["smd_rate"] = prt["smd_rate"].round(1)

    my300 = prt.query('placer=="MY300"')
    bm221 = prt.query('placer=="BM221"')

    my300["forcast"] = (
        my300["smd_count"] * 0.389
        + my300["smd_sum"] * 0.131
        + my300["qty"] * 3.048
        + my300["side"] * 37.5
        + my300["panel"] * 2.783
    ) - 15.384
    my300["forcast_rate"] = (my300["forcast"] - my300["smd_actual"]) / my300["forcast"]
    my300["forcast_rate"] = my300["forcast_rate"].round(1)
    my300["text"] = (
        "SMD开始:"
        + my300["smd_start"].astype(str)
        + ";SMD结束:"
        + my300["smd_end"].astype(str)
        + ";实际工时:"
        + (my300["smd_actual"] / 60).round(1).astype(str)
        + ";理论工时:"
        + (my300["forcast"] / 60).round(1).astype(str)
    )

    bm221["forcast"] = (
        bm221["smd_count"] * 0.95
        + bm221["smd_sum"] * 0.047
        + bm221["qty"] * 1.48
        + bm221["side"] * 0
        + bm221["panel"] * 0
    ) + 100.878
    bm221["forcast_rate"] = (bm221["forcast"] - bm221["smd_actual"]) / bm221["forcast"]
    bm221["forcast_rate"] = bm221["forcast_rate"].round(1)
    bm221["text"] = (
        "SMD开始:"
        + bm221["smd_start"].astype(str)
        + ";SMD结束:"
        + bm221["smd_end"].astype(str)
        + ";实际工时:"
        + (bm221["smd_actual"] / 60).round(1).astype(str)
        + ";理论工时:"
        + (bm221["forcast"] / 60).round(1).astype(str)
    )

    fig = make_subplots(rows=1, cols=1, shared_xaxes=True, vertical_spacing=0.02)
    fig.add_trace(
        go.Scatter(
            x=my300["prtno"],
            y=my300["smd_rate"],
            name="公式偏差",
            mode="lines+markers",
            text=my300["text"],
        ),
        row=1,
        col=1,
    )
    fig.add_trace(
        go.Scatter(
            x=my300["prtno"],
            y=my300["forcast_rate"],
            name="回归偏差",
            mode="lines+markers",
            text=my300["text"],
        ),
        row=1,
        col=1,
    )

    fig.update_layout(
        yaxis1=dict(
            tickvals=np.arange(-2, 2, 0.5),
            tickformat=".0%",
        ),
        title_text="MY300过去一个月:(理论工时-实际工时)/理论工时",
        height=400,
    )

    graph3 = dcc.Graph(figure=fig, config={"displayModeBar": False})

    fig = make_subplots(rows=1, cols=1, shared_xaxes=True, vertical_spacing=0.02)

    fig.add_trace(
        go.Scatter(
            x=bm221["prtno"],
            y=bm221["smd_rate"],
            name="公式偏差",
            mode="lines+markers",
            text=bm221["text"],
        ),
        row=1,
        col=1,
    )
    fig.add_trace(
        go.Scatter(
            x=bm221["prtno"],
            y=bm221["forcast_rate"],
            name="回归偏差",
            mode="lines+markers",
            text=bm221["text"],
        ),
        row=1,
        col=1,
    )

    fig.update_layout(
        yaxis1=dict(tickvals=np.arange(-2, 2, 0.5), tickformat=".0%"),
        title_text="BM221过去一个月:(理论工时-实际工时)/理论工时",
        height=400,
    )

    graph4 = dcc.Graph(figure=fig, config={"displayModeBar": False})

    # *-------插件工时-------------

    sql = (
        "select prtno,dept,fsqty,dip_count,dip_sum,dipstadate as dip_start,fsdate_act as dip_end,\
        dip_std_time,smd_sum from ssp.prt where fsdate_act>DATE_SUB(CURDATE(), INTERVAL 1 month) and \
            dipstadate is not null"
    )

    dip = read_sql(sql)

    for i in dip.itertuples():
        ts = pd.date_range(i.dip_start, i.dip_end, freq="t").to_series()
        ts = ts.loc[ts.dt.dayofweek < 6]
        x1 = ts.between_time("08:45", "11:45").size
        x2 = ts.between_time("13:05", "16:30").size
        dip.loc[i.Index, "dip_actual"] = x1 + x2

    dip["dip_actual"] = (dip["dip_actual"] / 60).round(1)
    dip["dip_rate"] = (dip["dip_std_time"] - dip["dip_actual"]) / dip["dip_std_time"]
    dip["dip_rate"] = dip["dip_rate"].round(1)

    dip["text"] = (
        "DIP开始:"
        + dip["dip_start"].astype(str)
        + ";DIP结束:"
        + dip["dip_end"].astype(str)
        + ";首样数量:"
        + dip["fsqty"].astype(str)
        + ";部门:"
        + dip["dept"].astype(str)
        + ";实际工时:"
        + dip["dip_actual"].astype(str)
        + ";理论工时:"
        + dip["dip_std_time"].astype(str)
    )

    dip_table = DashTabulator(
        data=dip.to_dict(orient="records"),
        theme="tabulator_site",
        options={
            "height": "400px",
            "clipboard": "copy",
            "selectable": True,
            "selectableRangeMode": "click",
        },
        downloadButtonType={
            "css": "btn btn-sm btn-secondary",
            "text": "Export",
            "type": "xlsx",
        },
        columns=[
            {
                "title": i,
                "field": i,
                "headerFilter": "select",
                "headerFilterParams": {"values": True},
            }
            for i in dip.columns
        ],
    )
    # conn.close()
    fig = make_subplots(rows=1, cols=1, shared_xaxes=True, vertical_spacing=0.02)

    fig.add_trace(
        go.Scatter(
            x=dip["prtno"],
            y=dip["dip_rate"],
            name="公式偏差",
            mode="lines+markers",
            text=dip["text"],
        ),
        row=1,
        col=1,
    )

    fig.update_layout(
        yaxis1=dict(tickvals=np.arange(-2, 2, 0.5), tickformat=".0%"),
        title_text="插件工时:(理论工时-实际工时)/理论工时",
        height=400,
    )

    graph5 = dcc.Graph(figure=fig, config={"displayModeBar": False})

    return [output1, graph3, graph4, graph5, dip_table]


def page5_output():
    sql = "select * from ssp.smddip where r_type=%s"
    df = read_sql(sql, params=["b"])
    df.columns = df.columns.str.lower()

    columns = [{"title": i, "field": i, "headerFilter": True} for i in df.columns]

    table = DashTabulator(
        data=df.to_dict(orient="records"),
        id="tab5_table",
        theme="tabulator_site",
        columns=columns,
        options={
            "height": "400px",
            "layout": "fitDataFill",
            "initialSort": [{"column": "id", "dir": "desc"}],
        },
    )
    output = dmc.Grid(
        [
            dmc.Col(table, span=10),
            dmc.Col(
                dmc.Stack(
                    [
                        dmc.Textarea(
                            id="new-pn-txt",
                            label="添加SMD新料号",
                            placeholder="从Excel复制粘贴料号，或手动输入料号，用逗号分隔",
                            minRows=14,
                        ),
                        dmc.Button(
                            "提交",
                            id="new-pn-btn",
                            fullWidth=True,
                            color="yellow",
                        ),
                    ]
                ),
                span=2,
            ),
        ],
    )
    return output


@callback(
    Output("global-notice", "children"),
    Input("new-pn-btn", "n_clicks"),
    State("new-pn-txt", "value"),
    State("user", "data"),
)
def submit_new_pn(n_clicks, value, user):
    if not value:
        raise PreventUpdate

    pn = set(re.findall("\w{10,}", value))
    if not pn:
        raise PreventUpdate

    nt_name = user.get("nt_name").title()
    sql = "insert into ssp.smddip(c_deltapn,owner,r_sequence) \
    select %s,%s,COALESCE((MAX(r_sequence) + 1), 1) FROM ssp.smddip \
        where not exists (select * from ssp.smddip where c_deltapn=%s)"
    params = [(i, nt_name, i) for i in pn]
    try:
        with pool.connection() as conn:
            with conn.cursor() as cu:
                cu.executemany(sql, params)
                conn.commit()
        return notice()
    except Exception as e:
        return notice(f"提交失败{e}", "error")


# @callback(
#     Output("content", "children"),
#     Input("notice", "children"),
# )
# def refresh_page5(child):
#     if not child:
#         raise PreventUpdate

#     type = child.get("props").get("type")
#     if type != "success":
#         raise PreventUpdate

#     return page5_output()


@callback(
    Output("tab6_table", "data"),
    Input("smbom-btn", "n_clicks"),
    State("prtno", "value"),
)
def smbom_btn(n_clicks, prtno):
    if not prtno:
        raise PreventUpdate

    sql = "select designno,deltapn,des,mfgname,mfgpn,packaging,checkcode,gmt_create \
        from ssp.smbom where prtno=%s"
    df = read_sql(sql, params=[prtno])
    df.columns = df.columns.str.lower()
    return df.to_dict(orient="records")


def dip_gantt_plan(user):
    area = user.get("area")
    # df = get_dip_data(area)
    df = pd.DataFrame()
    transfer1 = fac.Transfer(
        dataSource=[
            {
                "key": i.prt_id,
                "title": fac.Compact(
                    [
                        fac.Tag(content=i.smstatus),
                        fac.Tag(content=i.dept),
                        fac.Tag(content=i.prtno),
                        fac.Tag(content=i.qty),
                        fac.Tag(content=i.ee),
                    ],
                ),
            }
            for i in df.itertuples()
        ],
        id=id("dip-transfer1"),
    )
    now = datetime.now().strftime("%Y-%m-%d")
    column_labels = html.Div(
        [
            html.Div(
                f"{i + 8}:00",
                style={
                    "position": "absolute",
                    "left": f"{i * (100 / 13)}%",  # 12列平均分布
                    "top": "-25px",  # 在网格上方
                    "width": f"{100 / 13}%",
                    "textAlign": "center",
                    "color": "#666",
                    "fontSize": "12px",
                },
            )
            for i in range(13)
        ],
        style={"position": "relative"},
    )
    grid = Grid(
        id=id("grid"),
        cols=12,
        rowHeight=30,
        margin=[0, 0],
        containerPadding=[0, 0],
        isBounded=True,
        preventCollision=True,
        # children=children,
        # layout=layout,
    )
    grid = html.Div(
        [column_labels, grid],
        style={
            "position": "relative",
            "height": "129px",
            "backgroundColor": "white",
            "marginTop": "40px",
        },
    )
    process = fac.Progress(
        percent=0, steps=10, format={"prefix": "Loading:"}, id=id("dip-process")
    )

    date_picker = fac.DatePicker(id=id("dip-date-picker1"), value=now)
    button = dmc.Button("发布计划", id=id("dip-release-btn"))
    store = dcc.Store(id=id("first-load-store"), data=True)
    plan = fac.Flex(
        [fac.Space([date_picker, process]), transfer1, grid, button, store],
        vertical=True,
        gap=8,
    )
    return plan


def calculate_historical_stock(days=30):
    """
    计算历史库存变化

    获取过去指定天数(默认一个月)的库存变更记录，结合当前库存数量，
    计算每个库存项在历史每一天的库存水平。

    参数:
        days (int): 要查询的历史天数，默认为30天

    返回:
        DataFrame: 包含每个库存项在每天的历史库存数量
    """
    sql = f"""
        SELECT DATE(create_time) AS date, id AS stock_id, type, qty
        FROM stock_modify
        WHERE create_time >= DATE_SUB(CURDATE(), INTERVAL {days} DAY)
    """
    stock_modify = read_db(sql)

    # 获取当前库存
    sql = """
        SELECT id AS stock_id, qty FROM stock
    """
    current_stock = read_db(sql)

    # 计算每日变化量
    daily_changes = stock_modify.group_by(["date", "stock_id"]).agg(
        [
            (
                pl.col("qty").filter(pl.col("type") == "after").sum()
                - pl.col("qty").filter(pl.col("type") == "before").sum()
            ).alias("change")
        ]
    )

    if len(daily_changes) == 0:
        return pd.DataFrame(columns=["date", "stock_id", "qty"])

    # 获取日期范围
    min_date = daily_changes["date"].min()
    max_date = datetime.now().date()

    # 创建日期序列
    date_range = pl.date_range(
        min_date, max_date, interval="1d", closed="both", eager=True
    )
    # print(list(date_range), 2222222)
    date_df = pl.DataFrame({"date": date_range})

    # 创建库存ID列表
    stock_ids = current_stock.select("stock_id").unique()

    # 创建日期和库存ID的笛卡尔积
    all_combinations = date_df.join(stock_ids, how="cross")

    # 将变化数据合并到笛卡尔积中
    result_with_changes = all_combinations.join(
        daily_changes, on=["date", "stock_id"], how="left"
    ).with_columns(pl.col("change").fill_null(0))
    # print(result_with_changes, 1111111)

    # 计算每个库存项在每天的累计变化（倒序累计）
    result_with_cumulative = result_with_changes.sort(
        ["stock_id", "date"], descending=[False, True]
    ).with_columns(
        pl.col("change").cum_sum().over("stock_id").alias("cumulative_change")
    )

    # 合并当前库存数据，计算历史库存
    final_result = (
        result_with_cumulative.join(current_stock, on="stock_id", how="left")
        .with_columns(
            (pl.col("qty") - pl.col("cumulative_change")).alias("historical_qty")
        )
        .select(["date", "stock_id", "historical_qty"])
        .rename({"historical_qty": "qty"})
        .sort(["stock_id", "date"])
    )
    return final_result


def dip_progress(date):
    sql = "select * from ssp.prt_dip where date(plan_date)=%s"
    df1 = read_sql(sql, params=[date])

    sql = "select * from ssp.prt_delivery where date(plan_date)=%s"
    df2 = read_sql(sql, params=[date])

    if df1.empty and df2.empty:
        return fac.Empty()
    df1["type"] = "插件"
    df2["type"] = "快递"

    df = pd.concat([df1, df2], ignore_index=True)
    # df["batch_hour"] = df["batch_hour"] + df["offset_hour"]
    # df1 = df.loc[df["start_time"].notna() & df["end_time"].notna()]
    # fig = px.timeline(
    #     df1,
    #     x_start="start_time",
    #     x_end="end_time",
    #     y="prtno",
    #     color="owner",
    #     text="prtno",
    # )
    # fig.update_layout(template="ggplot2")
    # graph = dcc.Graph(figure=fig, config={"displayModeBar": False})
    df["start_time"] = pd.to_datetime(df["start_time"])
    df["end_time"] = pd.to_datetime(df["end_time"])
    df["start_time"] = df["start_time"].dt.strftime("%m-%d %H:%M")
    df["end_time"] = df["end_time"].dt.strftime("%m-%d %H:%M")
    table = dag.AgGrid(
        id=id("dip-table-left"),
        className="ag-theme-alpine compact",
        columnDefs=[
            {"headerName": "类型", "field": "type"},
            {"headerName": "区域", "field": "dip_area"},
            {"headerName": "项目号", "field": "prtno"},
            {"headerName": "数量", "field": "batch_qty"},
            # {"headerName": "工时", "field": "batch_hour"},
            {"headerName": "职责人", "field": "owner"},
            {"headerName": "开始时间", "field": "start_time"},
            {"headerName": "结束时间", "field": "end_time"},
            {"headerName": "备注", "field": "delivery_no"},
        ],
        rowData=df.to_dict(orient="records"),
        columnSize="autoSize",
        defaultColDef={
            "resizable": True,
            "sortable": True,
            "filter": True,
            "wrapHeaderText": True,
            "autoHeaderHeight": True,
        },
        dashGridOptions={
            # "rowSelection": "single",
            "stopEditingWhenCellsLoseFocus": True,
            "singleClickEdit": True,
            # "animateRows": "animate",
            "enableCellTextSelection": True,
            # "enableBrowserTooltips": True,
            "tooltipInteraction": True,
            "tooltipMouseTrack": True,
            "tooltipShowDelay": 200,
            # "domLayout": "autoHeight",
        },
        style={"height": "80vh"},
    )

    div = fuc.Scrollbars(
        table,
        style={
            "maxHeight": "80vh",
            "maxWidth": "100%",
            "border": "1px dashed #e1dfdd",
        },
        autoHide=False,
    )

    return table


def dip_progress_1(date):
    # y = calculate_historical_stock()
    # print(datetime.time(8, 45))
    # print(y.filter(pl.col("date") == datetime(2025, 3, 9).date()))

    colors = [
        "#64B5F6",  # 清新蓝
        "#81C784",  # 清新绿
        "#FFB74D",  # 温暖橙
        "#BA68C8",  # 优雅紫
        "#4FC3F7",  # 天空蓝
        "#AED581",  # 嫩叶绿
        "#FF8A65",  # 珊瑚色
        "#9575CD",  # 梦幻紫
        "#4DD0E1",  # 碧蓝色
        "#DCE775",  # 柠檬黄
    ]
    date = datetime.strptime(date, "%Y-%m-%d")
    start_time = date.replace(hour=7, minute=30, second=0, microsecond=0)
    end_time = date.replace(hour=20, minute=30, second=0, microsecond=0)
    sql = "select id,start_time,end_time,owner,batch_hour,offset_hour,prtno \
            from ssp.prt_dip where date(plan_date)=%s order by owner,id"
    df = read_sql(sql, params=[date.date()])
    if df.empty:
        return fac.Empty()
    df["duration"] = df["batch_hour"] + df["offset_hour"]

    # Group by owner and process each group
    for owner, group in df.groupby("owner"):
        # Initialize variables for each group
        std_start = None
        std_end = None

        # Process each row in the group
        for idx in group.index:
            if std_start is None:
                # First row in group
                std_start = (
                    group.loc[idx, "start_time"]
                    if pd.notnull(group.loc[idx, "start_time"])
                    else start_time
                )
            else:
                # Subsequent rows
                if pd.isnull(group.loc[idx, "start_time"]):
                    # If no start_time, use previous end time + 15min
                    std_start = std_end + pd.Timedelta(minutes=15)
                else:
                    # If has start_time, use it
                    std_start = group.loc[idx, "start_time"]

            # Calculate standard end time
            std_end = std_start + pd.Timedelta(hours=group.loc[idx, "duration"])

            # Update the dataframe with standard times
            df.loc[idx, "std_start_time"] = std_start
            df.loc[idx, "std_end_time"] = std_end

            # Set actual times equal to standard times if null
            if pd.isnull(df.loc[idx, "start_time"]):
                df.loc[idx, "start_time"] = std_start
            if pd.isnull(df.loc[idx, "end_time"]):
                df.loc[idx, "end_time"] = std_end

    # Add owner_group for y-axis ordering
    df["owner_group"] = pd.Categorical(df["owner"]).codes

    fig = go.Figure()

    # 为每个任务添加痕迹
    for i in df.itertuples():
        task_id = i.owner_group
        std_start = i.std_start_time
        std_end = i.std_end_time
        act_start = i.start_time
        act_end = i.end_time
        theoretical_trace = go.Scatter(
            x=[std_start, std_end, std_end, std_start],
            y=[task_id + 0.4, task_id + 0.4, task_id + 0.7, task_id + 0.7],
            # mode="lines",
            fill="toself",
            fillcolor="#E0E0E0",
            line={"width": 0},
            text=[i.prtno],
            textposition="bottom right",  # 或 "top", "bottom", "middle", "right" 等
            mode="lines+markers+text",  # 需要包含 "text"
        )
        fig.add_trace(theoretical_trace)

        # 实际用时条形
        actual_trace = go.Scatter(
            x=[act_start, act_end, act_end, act_start],
            y=[task_id, task_id, task_id + 0.3, task_id + 0.3],
            mode="lines",
            fill="toself",
            fillcolor=colors[i.Index],
            line={"width": 0},
            showlegend=False,
        )
        fig.add_trace(actual_trace)

    # 设置更美观的布局
    fig.update_layout(
        template="plotly_white",
        title={
            "text": "项目进度",
            "font": {"size": 24, "color": "#2c3e50"},
            "x": 0.5,
            "xanchor": "center",
        },
        plot_bgcolor="rgba(255,255,255,0.9)",
        paper_bgcolor="white",
        yaxis={
            "tickvals": list(df.owner_group),
            "ticktext": list(df.owner),
            "autorange": "reversed",
            "gridcolor": "rgba(233,233,233,0.8)",
            "title": "任务列表",
            "title_font": {"size": 16},
        },
        xaxis={
            "type": "date",
            "range": [start_time, end_time],
            "tickformat": "%H:%M",
            "gridcolor": "rgba(233,233,233,0.8)",
            "title": "时间",
            "title_font": {"size": 16},
        },
        showlegend=False,
        margin={"t": 60, "b": 60, "l": 60, "r": 60},
        hoverlabel={"bgcolor": "white", "font_size": 14},
        height=400,
    )
    return dcc.Graph(figure=fig, config={"displayModeBar": False})


def dip_progress_2(date):
    colors = [
        "#64B5F6",  # 清新蓝
        "#81C784",  # 清新绿
        "#FFB74D",  # 温暖橙
        "#BA68C8",  # 优雅紫
        "#4FC3F7",  # 天空蓝
        "#AED581",  # 嫩叶绿
        "#FF8A65",  # 珊瑚色
        "#9575CD",  # 梦幻紫
        "#4DD0E1",  # 碧蓝色
        "#DCE775",  # 柠檬黄
    ]
    sql = "select prtno,owner,offset_hour+batch_hour as hour,start_time,end_time \
    from ssp.prt_dip where date(plan_date)=%s"
    df = read_sql(sql, params=[date])
    df["owner"] = df["owner"].str.lower()

    layout = [i for i in cfg.hz_dip]
    layout = [
        {"i": "lili.fang", "x": 0, "y": 0, "w": 1, "h": 1, "static": True},
        {"i": "cc.zhu", "x": 0, "y": 2, "w": 1, "h": 1, "static": True},
        {"i": "zhibiao.ruan", "x": 0, "y": 4, "w": 1, "h": 1, "static": True},
        {"i": "qin.hong", "x": 0, "y": 6, "w": 1, "h": 1, "static": True},
    ]
    children = [
        fac.Text(i["i"].title(), id=i["i"], strong=True, underline=True) for i in layout
    ]

    for i in df.itertuples():
        y = [j["y"] for j in cfg.hz_dip if j["i"] == i.owner]
        y = y[0]
        l = {"w": i.hour, "h": 1, "x": 1, "y": y, "i": f"{i.Index}", "static": True}
        layout.append(l)
        p = dmc.Progress(
            value=100,
            label=i.prtno,
            h="100%",
            color=colors[i.Index],
            size=20,
            id=f"{i.Index}",
        )
        p = fac.Button(
            i.prtno,
            id=f"{i.Index}",
            block=True,
            # href="/scan/dip/?id=86",
            clickExecuteJsString='dash_clientside.set_props("button-click-execute-js-modal", {"visible": true})',
        )

        children.append(p)

    grid = Grid(
        id=id("grid"),
        cols=12,
        rowHeight=30,
        margin=[0, 0],
        containerPadding=[0, 0],
        isBounded=True,
        preventCollision=True,
        children=children,
        layout=layout,
    )
    # grid = html.Div(
    #     [column_labels, grid],
    #     style={
    #         "position": "relative",
    #         "height": "129px",
    #         "backgroundColor": "white",
    #         "marginTop": "40px",
    #     },
    # )
    modal = fac.Modal(
        "这是一个示例模态框",
        id="button-click-execute-js-modal",
        title="示例模态框",
    )
    div = html.Div(
        [
            grid,
            modal,
        ]
    )
    return div


def dip_execute(date, user):
    area = user.get("area")
    nt_name = user.get("nt_name")
    sql = "select * from ssp.prt_dip  \
        where date(plan_date)=%s and dip_area=%s and \
        (start_time is null or end_time is null)"
    df = read_sql(sql, params=[date, area])
    df["type"] = "dip"
    if area == "SH":
        sql = "select * from ssp.prt_delivery where start_time is null"
        df1 = read_sql(sql)
        df1["type"] = "delivery"
        df = pd.concat([df, df1], ignore_index=True)
    if df.empty:
        return fac.Empty()
    prt_id = df["prt_id"].tolist()
    sql = "select id as prt_id,proj,ee from ssp.prt where id in %s"
    prt = read_sql(sql, params=[prt_id])
    df = df.merge(prt, on="prt_id", how="left")
    df["fsdate_sch"] = df["fsdate_sch"].fillna(datetime.now())
    df = df.fillna(False)
    # df = df.sort_values(by=["owner", "id"])
    # 将owner等于nt_name的记录排在最前面
    df["is_owner"] = df["owner"].str.lower() == nt_name.lower()
    df = df.sort_values(by=["is_owner", "owner", "id"], ascending=[False, True, True])
    df = df.drop(columns=["is_owner"])

    modal = fac.Modal(
        fac.RadioGroup(
            options=[
                {"label": "假期", "value": "假期"},
                {"label": "合板", "value": "合板"},
                {"label": "缺料", "value": "缺料"},
                {"label": "磁芯", "value": "磁芯"},
                {"label": "外包", "value": "外包"},
                {"label": "其它", "value": "其它"},
            ],
            id=id("dip-delay-reason"),
        ),
        id=id("dip-delay-modal"),
        title="首样制作时间延期原因",
        renderFooter=True,
        okText="提交",
        cancelButtonProps={"disabled": True},
        okClickClose=False,
        maskClosable=False,
        closable=False,
    )

    modal2 = fac.Modal(
        fac.Input(id=id("dip-delivery-input"), addonBefore="快递单号"),
        id=id("dip-delivery-modal"),
        title="快递信息",
        renderFooter=True,
        okText="提交",
        cancelButtonProps={"disabled": True},
        okClickClose=False,
        maskClosable=False,
        closable=False,
    )

    div0 = [
        modal,
        modal2,
        dcc.Store(id=id("dip-delay-store"), data={}),
        dcc.Store(id=id("dip-delivery-store"), data={}),
    ]
    div1 = []
    owners = {i: [] for i in df["owner"].unique()}
    year, month, day = date.split("-")

    for i in df.itertuples():
        start_disabled = False
        end_disabled = True
        deadline = datetime.now()
        hours = i.batch_hour + i.offset_hour
        if i.start_time:
            start_disabled = True
            deadline = i.start_time + timedelta(hours=hours)
        if i.start_time and (not i.end_time):
            end_disabled = False

        description = fac.Text(i.proj, strong=True, style={"color": "white"})

        if i.urgent:
            color = "red"
        else:
            color = "green"
        extra_content = fac.Space(
            [
                fac.Text(f"{i.ee}", strong=True, style={"font-size": "18px"}),
                fac.Text(
                    f"计划:{month}/{day},截止:{i.fsdate_sch:%m/%d %H:%M}",
                    strong=True,
                    style={"color": color},
                ),
            ],
            direction="vertical",
            size=0,
        )
        if i.type == "dip":
            card = dip_card(
                index_name=i.owner,
                indexNameStyle={"fontWeight": "bold"},
                index_description=description,
                index_value=f"{i.prtno}-{i.batch_qty}台",
                extra_content=extra_content,
                footer_content=fac.Flex(
                    [
                        fac.Button(
                            "开始",
                            id={"type": id("start"), "index": f"{i.id}"},
                            style={"backgroundColor": "#A8E6CF"},
                            disabled=start_disabled,
                        ),
                        fac.Countdown(
                            value=deadline.strftime("%Y-%m-%d %H:%M:%S:%f"),
                            prefix=fac.Icon(icon="antd-bell"),
                            valueStyle={"fontSize": "20px"},
                            id={"type": id("countdown"), "index": f"{i.id}"},
                            key=hours,
                        ),
                        fac.Button(
                            "结束",
                            id={"type": id("end"), "index": f"{i.id}"},
                            style={"backgroundColor": "#FF8A65"},
                            disabled=end_disabled,
                        ),
                    ],
                    justify="space-between",
                ),
                footerContentStyle={"paddingTop": 0, "height": 32},
            )
        elif i.type == "delivery":
            card = dip_card(
                index_name=i.owner,
                indexNameStyle={"fontWeight": "bold"},
                index_description=description,
                index_value=f"{i.prtno}-{i.batch_qty}台",
                extra_content=description,
                footer_content=fac.Button(
                    "快递",
                    id={"type": id("delivery"), "index": f"{i.id}"},
                    style={"backgroundColor": "#BA68C8"},
                    disabled=start_disabled,
                    block=True,
                    key=i.prt_id,
                ),
                footerContentStyle={"paddingTop": 0, "height": 32},
            )

        # div.append(card)
        owners[i.owner].append(card)

    for i in owners:
        div1.append(
            fac.Divider(
                fac.Space(
                    [
                        fac.Avatar(
                            icon="antd-user",
                            style={"background": "#4551f5"},
                            size="small",
                        ),
                        fac.Title(i, level=4),
                    ],
                    align="start",
                ),
                innerTextOrientation="left",
            ),
        )
        div1.append(dmc.SimpleGrid(owners[i], cols=4))

    return fuc.Scrollbars(
        fac.Flex(div0 + div1, vertical=True),
        style={
            "maxHeight": "80vh",
            "maxWidth": "100%",
            "border": "1px dashed #e1dfdd",
        },
        autoHide=False,
    )


def dip_progress_layout():
    return dcc.Graph(id=id("dip-progress-graph"), config={"displayModeBar": False})


def dip_plan_data(date, user):
    area = user.get("area")
    if area == "SH":
        sql = "select a.id as prt_id,prtno,dept,smstatus,a.ee,qty as total_qty,\
            smtstadate,dip_area,findate_sch as remark,proj,a.board,smd_area,\
            dip_std_time as standard_hour,dip_remark,express_qty,b.project,placer\
            from prt a left join ssp.project b on a.project_id=b.id \
            where (smstatus in %s and dip_area=%s) \
            or (smstatus in %s and (dip_area!=%s and smd_area=%s))"
        params = [
            ["progok", "smtstart", "smtfinish", "fsfinish"],
            area,
            ["smtstart", "smtfinish"],
            area,
            area,
        ]
        df = read_sql(sql, params=params)
        # 不显示非本地的外包项目
        c1 = df["placer"] == "FLX2010"
        c2 = df["dip_area"] != area
        df = df.loc[~(c1 & c2)]
        df["smstatus"] = df["smstatus"].str.lower()
        c1 = df["smd_area"] == area
        c2 = df["dip_area"] != area
        df["standard_hour"] = np.where(c1 & c2, 0.2, df["standard_hour"])
        df["table"] = np.where(c1 & c2, "delivery", "dip")
        c1 = df["placer"] == "FLX2010"
        c2 = df["smstatus"] == "smtstart"
        df["smstatus"] = np.where(c1 & c2, "外包开始", df["smstatus"])
    else:
        sql = (
            "select a.id as prt_id,prtno,dept,smstatus,a.ee,qty as total_qty,smtstadate,\
            dip_area,findate_sch as remark,proj,a.board,dip_std_time as standard_hour,\
            smd_area,dip_remark,express_qty,b.project,placer\
            from prt a left join ssp.project b on a.project_id=b.id \
            where (smstatus in %s and dip_area=%s)"
        )
        params = [["smtstart", "smtfinish", "fsfinish"], area]
        df = read_sql(sql, params=params)
        df["smstatus"] = df["smstatus"].str.lower()
        c1 = df["smd_area"] == df["dip_area"]
        c2 = df["smstatus"] != "fsfinish"
        df["smstatus"] = np.where(c1 & c2, "当地项目", df["smstatus"])
        c1 = df["placer"] == "FLX2010"
        c2 = df["smstatus"] == "smtstart"
        df["smstatus"] = np.where(c1 & c2, "外包开始", df["smstatus"])
        df["table"] = "dip"

    params = df["prt_id"].tolist()

    sql = "select prt_id,batch_qty,id as batch,\
        (batch_hour+offset_hour)/batch_qty as each_hour \
        from ssp.prt_dip where prt_id in %s"
    df1 = read_sql(sql, params=[params])

    df1 = df1.groupby("prt_id", as_index=False).agg(
        {"batch_qty": "sum", "batch": "size", "each_hour": "last"}
    )

    sql = "select prt_id,batch_qty as delivery_qty,id as delivery_batch,end_time \
        from ssp.prt_delivery where prt_id in %s"
    df2 = read_sql(sql, params=[params])

    df2 = df2.groupby("prt_id", as_index=False).agg(
        {"delivery_qty": "sum", "delivery_batch": "size", "end_time": "last"}
    )
    df = df.merge(df1, on="prt_id", how="left").merge(df2, on="prt_id", how="left")
    df["delivery_qty"] = df["delivery_qty"].fillna(0)
    df["end_time"] = df["end_time"].dt.date
    today = datetime.today().date()
    yesterday = today - timedelta(days=1)

    df["smstatus"] = df["smstatus"].replace(
        {
            "progok": "编程完成",
            "smtstart": "贴片开始",
            "smtfinish": "贴片完成",
            "dipstart": "插件开始",
            "fsfinish": "首样完成",
        }
    )
    c1 = df["smstatus"] == "贴片完成"
    c2 = df["dip_area"] != area
    if area == "SH":
        c3 = df["delivery_qty"] >= df["total_qty"]
    else:
        c3 = df["delivery_qty"] > 0
    df["smstatus"] = np.where(c1 & c2, "待寄快递", df["smstatus"])

    df["smstatus"] = np.where(c1 & c3, "已寄快递", df["smstatus"])
    c0 = df["smstatus"] == "已寄快递"
    if area != "SH":
        c1 = df["end_time"] == today
        c2 = df["end_time"] == yesterday
        df["smstatus"] = np.where(c0 & c1, "今日快递", df["smstatus"])
        df["smstatus"] = np.where(c0 & c2, "昨日快递", df["smstatus"])
    df["smstatus"] = df["smstatus"].astype("category")
    df["smstatus"] = df["smstatus"].cat.set_categories(
        [
            "待寄快递",
            "今日快递",
            "昨日快递",
            "已寄快递",
            "首样完成",
            "外包开始",
            "当地项目",
            "贴片完成",
            "插件开始",
            "贴片开始",
            "编程完成",
        ]
    )
    c1 = df["smstatus"] == "待寄快递"
    df["batch_qty"] = np.where(c1, df["delivery_qty"], df["batch_qty"])
    df["batch"] = np.where(c1, df["delivery_batch"], df["batch"])
    df["batch_qty"] = df["batch_qty"].fillna(0)
    df["each_hour"] = df["each_hour"].fillna(0)
    df["batch_qty"] = df["total_qty"] - df["batch_qty"]

    df = df.loc[df["batch_qty"] > 0]
    # 上海的不显示已寄快递
    if area == "SH":
        df = df.loc[df["smstatus"] != "已寄快递"]
    # else:
    #     df = df.loc[df["smstatus"] != "贴片完成"]

    df["batch_hour"] = df["standard_hour"] * df["batch_qty"] / df["total_qty"]
    df["batch_hour"] = np.where(
        df["each_hour"] > 0, df["each_hour"] * df["batch_qty"], df["batch_hour"]
    )

    df["offset_hour"] = 0
    df["fsdate_sch"] = np.where(
        df["dip_area"] == "SH",
        df["smtstadate"] + pd.offsets.Day(3),
        df["smtstadate"] + pd.offsets.Day(4),
    )
    df["fsdate_sch"] = np.where(
        df["placer"] == "FLX2010",
        df["smtstadate"] + pd.offsets.Day(7),
        df["fsdate_sch"],
    )
    df["urgent"] = np.where(
        df["fsdate_sch"].dt.date <= datetime.now().date(), True, False
    )
    df["urgent"] = np.where(df["smstatus"] == "首样完成", False, df["urgent"])
    id_gen = IdGenerator()
    uid = id_gen.batch_generate_ids("dip", df.shape[0])
    df = df.assign(uid=uid)

    df = df.sort_values(by=["smstatus", "fsdate_sch"])

    # 右边表格数据
    sql = "select a.id,a.uid,a.batch_qty,batch_hour,offset_hour,owner,\
        b.prtno,b.smtfin_date,proj,ee,me,pm,b.dept,urgent,\
        dip_std_time as standard_hour,b.qty as total_qty \
        from ssp.prt_dip a \
        left join ssp.prt b on a.prt_id=b.id \
        left join ssp.user c on a.planner=c.nt_name \
        where plan_date=%s and c.area=%s"
    params = [date, area]
    df2 = read_sql(sql, params=params)
    df2["table"] = "dip"

    if area == "SH":
        sql = "select a.id,a.uid,a.batch_qty,batch_hour,offset_hour,owner,\
            b.prtno,b.smtfin_date,proj,ee,me,pm,b.dept,urgent,\
            dip_std_time as standard_hour,b.qty as total_qty \
            from ssp.prt_delivery a \
            left join ssp.prt b on a.prt_id=b.id \
            left join ssp.user c on a.planner=c.nt_name \
            where plan_date=%s and c.area=%s"
        params = [date, area]
        df3 = read_sql(sql, params=params)
        df3["table"] = "delivery"
        df2 = pd.concat([df2, df3])

    df2["urgent"] = df2["urgent"].astype(bool)
    data1 = df.to_dict(orient="records")
    data2 = df2.to_dict(orient="records")
    return data1, data2


def dip_statistics():
    sql = "select a.id,prt_id,offset_hour+batch_hour as hour,batch_qty,a.dip_area,\
        dip_sum*batch_qty as dip_sum,end_time,b.prtno,b.proj,b.dept,\
        c.project,b.board,b.qty \
        from ssp.prt_dip a \
        left join prt b on a.prt_id=b.id \
        left join ssp.project c on b.project_id=c.id \
        where end_time is not null"
    df = read_sql(sql)
    df["week"] = df["end_time"].dt.isocalendar().week
    df = df.loc[df["week"] > 15]
    week = datetime.now().isocalendar().week
    c1 = df["week"] == week - 1
    c2 = df["dip_area"] == "HZ"
    df_last = df.loc[c1 & c2]

    sql = "select prt_id,count(*) as batch from ssp.prt_dip where prt_id in %s and id not in %s group by prt_id"
    df1 = read_sql(sql, params=[df_last["prt_id"].tolist(), df_last["id"].tolist()])

    df_last = df_last.merge(df1, on="prt_id", how="left")
    df_last["category"] = np.where(df_last["batch"].isna(), "首样", "非首样")

    df_last_1 = df_last.groupby(["dept"], as_index=False).agg(
        hour=("hour", "sum"),
    )
    graph3 = fact.AntdPie(
        data=df_last_1.to_dict(orient="records"),
        colorField="dept",
        angleField="hour",
        height=250,
        label={"type": "inner", "content": "{percentage}"},
        innerRadius=0.5,
    )
    df_last_2 = df_last.groupby(["category"], as_index=False).agg(
        hour=("hour", "sum"),
    )
    df_last_3 = df_last.groupby(["prt_id"], as_index=False).agg(
        prtno=("prtno", "first"),
        dept=("dept", "first"),
        project=("project", "first"),
        board=("board", "first"),
        batch_qty=("batch_qty", "sum"),
        hour=("hour", "sum"),
        category=("category", "first"),
        qty=("qty", "first"),
    )
    table = dag.AgGrid(
        className="ag-theme-quartz",
        columnDefs=[
            {"headerName": "项目号", "field": "prtno", "width": 120},
            {"headerName": "部门", "field": "dept", "width": 100},
            {"headerName": "项目", "field": "project", "width": 250},
            {"headerName": "机种", "field": "board", "width": 200},
            {"headerName": "总数量", "field": "qty", "width": 80},
            {"headerName": "样制数量", "field": "batch_qty", "width": 80},
            {"headerName": "工时", "field": "hour", "width": 80},
            {"headerName": "类型", "field": "category", "width": 80},
        ],
        rowData=df_last_3.to_dict(orient="records"),
        # columnSize="responsiveSizeToFit",
        defaultColDef={
            "resizable": True,
            "sortable": True,
            "filter": True,
            "wrapHeaderText": True,
            "autoHeaderHeight": True,
        },
        dashGridOptions={
            # "rowSelection": "single",
            "stopEditingWhenCellsLoseFocus": True,
            "singleClickEdit": True,
            # "animateRows": "animate",
            "enableCellTextSelection": True,
        },
        style={"height": "450px"},
    )
    graph4 = fact.AntdPie(
        data=df_last_2.to_dict(orient="records"),
        colorField="category",
        angleField="hour",
        height=250,
        label={"type": "inner", "content": "{percentage}"},
        innerRadius=0.5,
    )

    df = df.groupby(["week", "dip_area"], as_index=False).agg(
        hour=("hour", "sum"),
        batch_qty=("batch_qty", "sum"),
        dip_sum=("dip_sum", "sum"),
    )
    df["hour_loading"] = df["hour"]
    df["hour_loading"] = np.where(
        df["dip_area"] == "SH", df["hour_loading"] / 5, df["hour_loading"]
    )
    df["hour_loading"] = np.where(
        df["dip_area"] == "HZ", df["hour_loading"] / 3.4, df["hour_loading"]
    )
    df["hour_loading"] = np.where(
        df["dip_area"] == "WH", df["hour_loading"] / 2.4, df["hour_loading"]
    )
    df["hour_loading"] = df["hour_loading"].round(1)
    df = df.sort_values(by=["week", "dip_area"])
    df["hour"] = df["hour"].round(1)
    graph1 = fact.AntdLine(
        data=df.to_dict(orient="records"),
        xField="week",
        yField="hour",
        seriesField="dip_area",
        # isStack=True,
        # smooth=True,
        color=["#F58E8E", "#22C55E", "#FBBF24", "#3B82F6"],
        label={
            "position": "center",
            # "formatter": {"func": "({ loading }) => `${(loading * 100).toFixed(0)}%`"},
        },
        height=200,
        # animation=True,
        # seriesField="owner1",
        # isStack=True,
    )
    graph2 = fact.AntdLine(
        data=df.to_dict(orient="records"),
        xField="week",
        yField="hour_loading",
        seriesField="dip_area",
        # isStack=True,
        # smooth=True,
        color=["#F58E8E", "#22C55E", "#FBBF24", "#3B82F6"],
        label={
            "position": "center",
            # "formatter": {"func": "({ loading }) => `${(loading * 100).toFixed(0)}%`"},
        },
        height=200,
        # animation=True,
        # seriesField="owner1",
        # isStack=True,
    )
    return fuc.Scrollbars(
        fac.Flex(
            [
                graph1,
                graph2,
                fac.Flex([graph3, graph4], justify="space-between"),
                table,
            ],
            vertical=True,
        ),
        style={
            "maxHeight": "80vh",
            "maxWidth": "100%",
            "border": "1px dashed #e1dfdd",
        },
        autoHide=False,
    )


def dip_plan_layout(user):
    cl = px.colors.sequential.Reds
    area = user.get("area")
    table1 = dag.AgGrid(
        id=id("dip-table-left"),
        className="ag-theme-alpine compact",
        columnDefs=[
            # {"headerName": "uid", "field": "uid"},
            {
                "headerName": "",
                "cellRenderer": "DipAddButton",
                "lockPosition": "left",
                "maxWidth": 18,
                "filter": False,
                "cellStyle": {"padding": 0},
                "pinned": "left",
            },
            {
                "headerName": "状态",
                "field": "smstatus",
                "width": 70,
                "cellStyle": {
                    "styleConditions": [
                        {
                            "condition": "params.value == '今日快递'",
                            "style": {"backgroundColor": cl[8], "padding": 0},
                        },
                        {
                            "condition": "params.value == '昨日快递'",
                            "style": {"backgroundColor": cl[7], "padding": 0},
                        },
                        {
                            "condition": "params.value == '已寄快递'",
                            "style": {"backgroundColor": cl[6], "padding": 0},
                        },
                        {
                            "condition": "params.value == '待寄快递'",
                            "style": {"backgroundColor": cl[8], "padding": 0},
                        },
                        {
                            "condition": "params.value == '首样完成'",
                            "style": {"backgroundColor": cl[4], "padding": 0},
                        },
                        {
                            "condition": "params.value == '外包开始'",
                            "style": {"backgroundColor": cl[3], "padding": 0},
                        },
                        {
                            "condition": "params.value == '当地项目'",
                            "style": {"backgroundColor": cl[2], "padding": 0},
                        },
                        {
                            "condition": "params.value == '贴片完成'",
                            "style": {"backgroundColor": cl[1], "padding": 0},
                        },
                        {
                            "condition": "params.value == '贴片开始'",
                            "style": {"backgroundColor": cl[0], "padding": 0},
                        },
                    ],
                    "defaultStyle": {"backgroundColor": "#ffffff", "padding": 0},
                },
                "pinned": "left",
            },
            {
                "headerName": "项目号",
                "field": "prtno",
                "width": 105,
                "pinned": "left",
                "cellStyle": {"padding": 0},
            },
            {
                "headerName": "机种名",
                "field": "board",
                "width": 150,
                "tooltipField": "project",
                "tooltipComponent": "CustomTooltipHeaders",
                "cellStyle": {"padding": 0},
            },
            {
                "headerName": "部门",
                "field": "dept",
                "width": 80,
                "cellStyle": {"padding": 0},
            },
            {
                "headerName": "总量",
                "field": "total_qty",
                "width": 50,
                "cellStyle": {"padding": 0},
                "headerStyle": {"padding": 0},
            },
            {
                "headerName": "剩余",
                "field": "batch_qty",
                "width": 50,
                "cellStyle": {"padding": 0},
            },
            {
                "headerName": "批次",
                "field": "batch",
                "width": 50,
                "cellStyle": {"padding": 0},
            },
            {
                "headerName": "首样截止",
                "field": "fsdate_sch",
                "width": 90,
                "valueGetter": {
                    "function": "d3.timeParse('%Y-%m-%dT%H:%M:%S')(params.data.fsdate_sch)"
                },
                "valueFormatter": {
                    "function": "params.value?d3.timeFormat('%m/%d %H:%M')(params.value):''"
                },
                "cellStyle": {"padding": 0},
            },
            {
                "headerName": "贴片开始",
                "field": "smtstadate",
                "width": 90,
                "valueGetter": {
                    "function": "d3.timeParse('%Y-%m-%dT%H:%M:%S')(params.data.smtstadate)"
                },
                "valueFormatter": {
                    "function": "params.value?d3.timeFormat('%m/%d %H:%M')(params.value):''"
                },
                "cellStyle": {"padding": 0},
            },
            {
                "headerName": "工时",
                "field": "batch_hour",
                "valueFormatter": {"function": "d3.format('.1f')(params.value)"},
                "width": 50,
                "cellStyle": {"padding": 0},
            },
            {
                "headerName": "备注",
                "field": "dip_remark",
                "width": 100,
                "editable": True,
                "cellStyle": {"padding": 0},
                "tooltipField": "dip_remark",
                "tooltipComponent": "CustomTooltipHeaders",
            },
        ],
        defaultColDef={
            "resizable": True,
            "sortable": True,
            "filter": True,
            "wrapHeaderText": True,
            "autoHeaderHeight": True,
        },
        dashGridOptions={
            # "rowSelection": "single",
            "stopEditingWhenCellsLoseFocus": True,
            "singleClickEdit": True,
            # "animateRows": "animate",
            "enableCellTextSelection": True,
            # "enableBrowserTooltips": True,
            "tooltipInteraction": True,
            "tooltipMouseTrack": True,
            "tooltipShowDelay": 200,
        },
        style={"height": "80vh"},
        getRowId="params.data.uid",
    )
    table2 = dag.AgGrid(
        id=id("dip-table-right"),
        className="ag-theme-alpine compact",
        columnDefs=[
            # {"headerName": "uid", "field": "uid"},
            {
                "headerName": "",
                "cellRenderer": "DipDeleteButton",
                "lockPosition": "left",
                "maxWidth": 18,
                "filter": False,
                "cellStyle": {"padding": 0},
                "pinned": "left",
            },
            {
                "headerName": "项目号",
                "field": "prtno",
                "width": 105,
                "pinned": "left",
                "cellStyle": {"padding": 0},
            },
            {
                "headerName": "职责人",
                "field": "owner",
                "editable": True,
                # "cellEditor": {"function": "DMC_Select"},
                # "cellEditorParams": {
                #     "options": cfg.dip_user.get(area),
                #     "clearable": True,
                #     "shadow": "xl",
                # },
                # "cellEditorPopup": True,
                "cellEditor": "agSelectCellEditor",
                "cellEditorParams": {
                    "values": cfg.dip_user.get(area),
                },
                "width": 100,
                "cellStyle": {"padding": 0},
            },
            {
                "headerName": "批量",
                "field": "batch_qty",
                "editable": True,
                "cellEditor": "agNumberCellEditor",
                "width": 50,
                "cellStyle": {"padding": 0},
            },
            # {
            #     "headerName": "需求日期",
            #     "field": "demand_date",
            #     "editable": True,
            #     "cellEditor": "agDateStringCellEditor",
            # },
            {
                "headerName": "修正",
                "field": "offset_hour",
                "editable": True,
                "cellEditor": "agNumberCellEditor",
                "width": 50,
                "cellEditorParams": {
                    "precision": 1,
                    "step": 0.1,
                    "showStepperButtons": True,
                },
                "cellStyle": {"padding": 0},
                # "pinned": "left",
            },
            {
                "headerName": "工时",
                "field": "batch_hour",
                "valueFormatter": {"function": "d3.format('.1f')(params.value)"},
                "width": 50,
                "cellStyle": {"padding": 0},
            },
            {
                "headerName": "紧急",
                "field": "urgent",
                "width": 50,
                "editable": True,
                "cellEditor": "agCheckboxCellEditor",
                "cellRenderer": "agCheckboxCellRenderer",
                "cellStyle": {"padding": 0},
            },
        ],
        # columnSize="sizeToFit",
        defaultColDef={
            # "editable": True,
            "resizable": True,
            "sortable": True,
            "filter": True,
            "wrapHeaderText": True,
            "autoHeaderHeight": True,
        },
        dashGridOptions={
            # "rowSelection": "single",
            "stopEditingWhenCellsLoseFocus": True,
            "singleClickEdit": True,
            # "animateRows": "animate",
            "enableCellTextSelection": True,
        },
        style={"height": "80vh"},
        getRowId="params.data.uid",
    )
    popup_card = fac.PopupCard(
        id=id("dip-popup-card"), visible=False, title="项目信息", draggable=True
    )
    return fac.Splitter(
        items=[
            {
                "children": [table1, popup_card],
                "defaultSize": "62%",
            },
            {
                "children": table2,
                "defaultSize": "38%",
            },
        ],
        style={
            "height": "100%",
            "boxShadow": "0 0 10px rgba(0, 0, 0, 0.1)",
        },
    )
    return fac.Flex([table1, table2, popup_card])


def dip_layout(user):
    process = fac.Statistic(
        valueStyle={"fontSize": "18px"},
        prefix="Loading",
        suffix="%",
        id=id("dip-loading"),
    )
    release_button = fac.Button("发布计划", id=id("dip-release-btn"), danger=True)

    date_picker = fac.Space(
        [
            fac.Text("计划日期", strong=True),
            fac.DatePicker(id=id("dip-date-picker"), value=datetime.now().date()),
        ]
    )
    # plan = fac.Flex([table1, table2])
    new_plan = fac.Button("临时计划", id=id("dip-new-btn"))
    new_modal = fac.Modal(
        fac.Form(
            [
                fac.FormItem(fac.Select(id=id("dip-new-rd")), label="工程师"),
                fac.FormItem(fac.Input(id=id("dip-new-proj")), label="机种名"),
                fac.FormItem(fac.InputNumber(id=id("dip-new-qty")), label="数量"),
                fac.FormItem(fac.InputNumber(id=id("dip-new-hour")), label="工时"),
            ]
        ),
        id=id("dip-new-modal"),
        title="临时计划",
        renderFooter=True,
        okText="提交",
        cancelText="取消",
        cancelButtonProps={"danger": True},
        okButtonProps={"disabled": True},
        # okClickClose=False,
    )
    tabs = dmc.Tabs(
        [
            dmc.TabsList(
                [
                    # dmc.Tab("新增计划", value="1", color="green"),
                    dmc.Tab("计划编排", value="1", color="blue"),
                    dmc.Tab("计划执行", value="2", color="orange"),
                    dmc.Tab("计划进度", value="3", color="green"),
                    dmc.Tab("数据统计", value="4", color="red"),
                    fac.Space(
                        [new_plan, new_modal, date_picker, process, release_button],
                        style={"margin-left": "auto"},
                        size=20,
                    ),
                ],
            ),
            dmc.Space(h=5),
            # dmc.TabsPanel(id=id("dip-new"), value="1"),
            dmc.TabsPanel(dip_plan_layout(user), value="1"),
            dmc.TabsPanel(id=id("dip-execute"), value="2"),
            dmc.TabsPanel(id=id("dip-progress"), value="3"),
            dmc.TabsPanel(dip_statistics(), value="4"),
        ],
        value="2",
        id=id("dip-tabs"),
    )
    return tabs


def smd_plan_data(date):
    sql = "select * from prt where smstatus in %s"
    params = [("planning", "PlanOK", "progok")]
    df = read_sql(sql, params=params)

    sql = "select b.prtno,b.leadtime,b.id,a.plan_date \
        from prt_smd a join prt b on a.prt_id=b.id where plan_date=%s"
    params = [date]
    df1 = read_sql(sql, params=params)
    df.columns = df.columns.str.lower()

    df["pcb_date"] = pd.to_datetime(df["pcbstatus"], errors="coerce")
    df["mat_ready_date"] = pd.to_datetime(df["mat_ready_date"], errors="coerce")
    c1 = df["pcb_date"].notna()
    c2 = df["pcb_date"].dt.time != pd.Timestamp(0).time()
    c3 = df["mat_ready_date"].notna()
    c4 = ~df["id"].isin(df1["id"])
    df = df[c1 & c2 & c3 & c4]
    df["earliest_start_date"] = df[["pcb_date", "mat_ready_date"]].max(axis=1)
    df["earliest_start_date"] = pd.to_datetime(
        df["earliest_start_date"], errors="coerce"
    ).dt.normalize()
    df["latest_start_date"] = df["earliest_start_date"] + pd.DateOffset(days=3)
    df = df.loc[df["latest_start_date"] > datetime.now()]

    df["pcb_date"] = df["pcb_date"].dt.strftime("%m/%d")
    df["mat_ready_date"] = df["mat_ready_date"].dt.strftime("%m/%d")
    df["mat1_date"] = df["mat1_date"].dt.strftime("%m/%d")
    df["mat2_date"] = df["mat2_date"].dt.strftime("%m/%d")
    df["smd_sum"] = df["smd_sum"] * df["qty"]
    # df["drag"] = 1
    # df1["drag"] = 0
    df = df.sort_values(by="fsdate_sch")

    left_table = dag.AgGrid(
        id=id("smd-table-left"),
        className="ag-theme-alpine compact",
        rowData=df.to_dict(orient="records"),
        columnDefs=[
            # {
            #     "headerName": "id",
            #     "field": "id",
            #     "width": 80,
            #     "pinned": "left",
            #     "cellStyle": {"padding": 1},
            # },
            {
                "headerName": "项目号",
                "field": "prtno",
                "width": 125,
                "pinned": "left",
                "cellStyle": {"padding": 1},
                "rowDrag": True,
            },
            # {
            #     "headerName": "设备",
            #     "field": "placer",
            #     "width": 60,
            #     "cellStyle": {"padding": 1},
            # },
            {
                "headerName": "部门",
                "field": "dept",
                "width": 90,
                "cellStyle": {"padding": 1},
            },
            {
                "headerName": "工时",
                "field": "leadtime",
                "width": 60,
                "cellStyle": {"padding": 1},
            },
            {
                "headerName": "点数",
                "field": "smd_sum",
                "width": 60,
                "cellStyle": {"padding": 1},
            },
            {
                "headerName": "种类",
                "field": "smd_count",
                "width": 60,
                "cellStyle": {"padding": 1},
            },
            {
                "headerName": "交期",
                "field": "fsdate_sch",
                "width": 60,
                "cellStyle": {"padding": 1},
                "valueFormatter": {
                    "function": "d3.timeFormat('%m/%d')(d3.isoParse(params.value))"
                },
            },
            {
                "headerName": "编程",
                "field": "prog_date",
                "width": 60,
                "cellStyle": {"padding": 1},
                "valueFormatter": {
                    "function": "d3.timeFormat('%m/%d')(d3.isoParse(params.value))"
                },
            },
            {
                "headerName": "卷料",
                "field": "mat2_date",
                "width": 60,
                "cellStyle": {"padding": 1},
            },
            {
                "headerName": "散料",
                "field": "mat1_date",
                "width": 60,
                "cellStyle": {"padding": 1},
            },
            {
                "headerName": "板齐",
                "field": "pcb_date",
                "width": 60,
                "cellStyle": {"padding": 1},
            },
            {
                "headerName": "料齐",
                "field": "mat_ready_date",
                "width": 60,
                "cellStyle": {"padding": 1},
            },
            {
                "headerName": "最早可开始",
                "field": "earliest_start_date",
                "width": 60,
                "valueFormatter": {
                    "function": "d3.timeFormat('%m/%d')(d3.isoParse(params.value))"
                },
                "cellStyle": {"padding": 1},
            },
            {
                "headerName": "最晚需开始",
                "field": "latest_start_date",
                "width": 60,
                "valueFormatter": {
                    "function": "d3.timeFormat('%m/%d')(d3.isoParse(params.value))"
                },
                "cellStyle": {"padding": 1},
            },
        ],
        defaultColDef={
            "resizable": True,
            "sortable": True,
            "filter": True,
            "wrapHeaderText": True,
            "autoHeaderHeight": True,
        },
        dashGridOptions={
            # "rowSelection": "single",
            # "stopEditingWhenCellsLoseFocus": True,
            # "singleClickEdit": True,
            "animateRows": "animate",
            # "enableCellTextSelection": True,
            # "enableBrowserTooltips": True,
            # "tooltipInteraction": True,
            # "tooltipMouseTrack": True,
            # "tooltipShowDelay": 200,
            "rowDragManaged": True,
            "rowDragEntireRow": True,
            # "rowDragMultiRow": True,
            "suppressMoveWhenRowDragging": True,
        },
        style={"height": "80vh"},
        getRowId="params.data.id",
    )
    right_table = dag.AgGrid(
        id=id("smd-table-right"),
        className="ag-theme-alpine compact",
        rowData=df1.to_dict(orient="records"),
        columnDefs=[
            # {
            #     "headerName": "id",
            #     "field": "id",
            #     "width": 80,
            #     "pinned": "left",
            #     "cellStyle": {"padding": 1},
            #     "rowDrag": True,
            # },
            {
                "headerName": "项目号",
                "field": "prtno",
                "width": 125,
                "pinned": "left",
                "cellStyle": {"padding": 1},
                "rowDrag": True,
            },
            {
                "headerName": "设备",
                "field": "placer",
                "width": 125,
                "pinned": "left",
                "cellStyle": {"padding": 1},
                "rowDrag": True,
            },
            {
                "headerName": "工时",
                "field": "leadtime",
                "width": 100,
                "cellStyle": {"padding": 1},
            },
        ],
        defaultColDef={
            "resizable": True,
            "sortable": True,
            "filter": True,
            "wrapHeaderText": True,
            "autoHeaderHeight": True,
        },
        dashGridOptions={
            # "rowSelection": "single",
            # "stopEditingWhenCellsLoseFocus": True,
            # "singleClickEdit": True,
            # "animateRows": "animate",
            # "enableCellTextSelection": True,
            # "enableBrowserTooltips": True,
            # "tooltipInteraction": True,
            # "tooltipMouseTrack": True,
            # "tooltipShowDelay": 200,
            "rowDragManaged": True,
            "rowDragEntireRow": True,
            "suppressMoveWhenRowDragging": True,
        },
        style={"height": "80vh"},
        getRowId="params.data.id",
    )

    div = fac.Splitter(
        items=[
            {"children": left_table, "defaultSize": "70%"},
            {"children": right_table, "defaultSize": "30%"},
        ],
        style={
            "height": "100%",
            "boxShadow": "0 0 10px rgba(0, 0, 0, 0.1)",
        },
    )

    # div = fac.Splitter(
    #     items=[
    #         {"children": dcc.Markdown(analysis), "defaultSize": "30%"},
    #         # {"children": graph, "defaultSize": "70%"},
    #     ],
    #     style={
    #         "height": "100%",
    #         "boxShadow": "0 0 10px rgba(0, 0, 0, 0.1)",
    #     },
    # )
    return df, left_table


def smd_layout(user):
    process = fac.Statistic(
        valueStyle={"fontSize": "18px"},
        prefix=fac.Text("Loading:", strong=True, style={"fontSize": "18px"}),
        suffix="%",
        id=id("smd-loading"),
    )
    release_button = fac.Button("发布计划", id=id("smd-release-btn"), danger=True)
    date = datetime.now().date()

    date_picker = fac.Space(
        [
            fac.Text("计划日期", strong=True),
            fac.DatePicker(id=id("smd-date-picker"), value=date),
        ]
    )
    ai_plan = fac.Button(
        "AI排产",
        clickExecuteJsString="dash_clientside.set_props('pages-sm-layout-ai-modal', {'visible': true})",
        type="primary",
    )
    df, left_table = smd_plan_data(date)
    df1 = df[
        [
            "prtno",
            "leadtime",
            "smd_sum",
            "smd_count",
            "earliest_start_date",
            "latest_start_date",
        ]
    ]
    df1.columns = [
        "项目名称",
        "贴片用时",
        "贴片点数",
        "材料种类",
        "最早可开始日期",
        "最晚需开始日期",
    ]
    prompt = f"""
你是一名SMD生产计划排程专家，负责为SMD产线安排生产计划  
您的目标是充分利用工作时间和设备资源，生成一个最优化的生产计划方案

**需排产项目清单如下(注:贴片用时单位是小时):**  
{df1.to_string()}

**请严格遵循以下原则和约束进行排产：**  

**计划开始时间：**  
- 首要依据是项目清单中的最早可开始日期，不能早于最早可开始日期  
- 满足上述条件后，充分利用工作时间和设备资源，尽早开始排产  

**计划完成时间：**  
- 计划开始时间确定后，计划完成时间=计划开始时间+贴片用时
- 如果跨非工作时间,则加上非工作时间的间隔时间

**设备资源：**  
- 共有3台贴片机：MY300, BM221, W2。
- 不同设备独立安排项目，合理分配项目，保证每日每台设备的负载均衡，确保设备利用率最大化

**设备特性：**  
- MY300： 适合生产点数少的项目
- BM221： 适合生产点数适中的项目
- W2： 适合生产点数多的项目

**工作时间：**  
- 每日工作时间为早上9:00至下午16:30
- 午休时间(11:30至13:30)不安排生产

**换线时间(项目切换时间)：**  
- 同一台设备上，不同项目之间必须预留40分钟的间隔时间

**加班时间：**
- 11:30-13:30和16:30-18:00
- 仅当所有项目的计划开始时间无法满足最晚需开始日期时再考虑加班
"""
    modal = fac.Modal(
        [dcc.Markdown(prompt, id=id("ai-prompt")), html.Div(id=id("ai-result"))],
        id=id("ai-modal"),
        title="AI排产",
        width="90%",
        renderFooter=True,
        okClickClose=False,
    )

    tabs = dmc.Tabs(
        [
            dmc.TabsList(
                [
                    dmc.Tab("计划编排", value="1", color="blue"),
                    dmc.Tab("计划执行", value="2", color="orange"),
                    fac.Space(
                        [ai_plan, modal, process, release_button],
                        style={"margin-left": "auto"},
                        size=20,
                    ),
                ],
            ),
            dmc.Space(h=5),
            dmc.TabsPanel(left_table, value="1"),
            # dmc.TabsPanel(id=id("dip-execute"), value="2"),
            # dmc.TabsPanel(id=id("dip-progress"), value="3"),
            # dmc.TabsPanel(dip_statistics(), value="4"),
        ],
        value="1",
        id=id("smd-tabs"),
    )
    return tabs


def project_layout():
    tabs = dmc.Tabs(
        [
            dmc.TabsList(
                [
                    dmc.Tab("项目接收", value="1", color="blue"),
                    dmc.Tab("项目调整", value="2", color="orange"),
                ],
            ),
            dmc.Space(h=5),
            dmc.TabsPanel(project_review(), value="1"),
            dmc.TabsPanel(project_modify(), value="2"),
        ],
        value="2",
        id=id("project-tabs"),
    )
    return tabs


@callback(
    Output("tab5_table", "data"),
    Input("tab5_table", "cellEdited"),
    State("tab5_table", "data"),
)
def tab5_table_cell_edited(cell, data):
    if not cell:
        raise PreventUpdate

    df = pd.DataFrame(data)
    rowid = df["id"] == cell.get("row").get("id")
    df.loc[rowid, df.columns.str.startswith("2")] = None
    col = cell.get("column")
    if col == "start":
        start = cell.get("value")
        duration = cell.get("row").get("duration")
    else:
        start = cell.get("row").get("start")
        duration = cell.get("value")

    prt_id = df.loc[rowid, "id"].iloc[0]
    sql = "replace into ssp.dip_plan(prt_id,start,duration) values(%s,%s,%s)"
    params = [prt_id, start, duration]
    with pool.connection() as conn:
        with conn.cursor() as cu:
            cu.execute(sql, params)
            conn.commit()
    #
    try:
        l = df.columns.get_loc(start)
        df.loc[rowid, df.columns[l : l + duration]] = 10
    except Exception:
        pass
    # if col == "start":
    #     try:
    #         df.loc[rowid, cell.get("value")] = 10
    #     except:
    #         pass
    # else:
    #     duration = cell.get("value")
    #     start = cell.get("row").get("start")
    #     l = df.columns.get_loc(start)
    #     try:
    #         df.loc[rowid, df.columns[l : l + duration]] = 10
    #     except:
    #         pass
    return df.to_dict(orient="records")


@callback(
    Output("global-notice", "children"),
    Output(id("project-table"), "rowData"),
    Input(id("project-submit"), "n_clicks"),
    State(id("project-table"), "rowData"),
    State("user", "data"),
)
def project_review_submit(n_clicks, data, user):
    if not n_clicks:
        raise PreventUpdate

    df = pd.DataFrame(data)

    if "action" not in df.columns:
        raise PreventUpdate

    df1 = df.loc[df["action"] == "接收"]
    df2 = df.loc[df["action"] == "删除"]
    dfx = df.loc[df["action"].isna()]

    if not df2.empty:  # 删除项目
        for i in df2.itertuples(index=False):
            db.delete("ssp.prt_temp", {"id": i.id})

    if not df1.empty:  # 接收项目
        nt_name = user.get("nt_name")
        df1.drop(["scheme", "approved", "action", "ee_area"], axis=1, inplace=True)
        for i in df1.itertuples(index=False):
            area = i.area
            smd_area = i.smd_area[0]
            dip_area = i.dip_area[0]
            sql = "select max(right(prtno,7)) as prtno from ssp.prt where prtno like %s"
            year_month = f"{datetime.now():%y%m}"

            res = db.execute_fetchone(sql, [f"%{year_month}%"])
            if x := res["prtno"]:
                prtno = f"{i.dip_area}{smd_area}{dip_area}{int(x) + 1}"
            else:
                prtno = f"{i.dip_area}{smd_area}{dip_area}{year_month}001"

            data: dict = i._asdict()
            data["prtno"] = prtno
            data.pop("id", None)
            db.insert("ssp.prt", data)
            db.delete("ssp.prt_temp", {"id": i.id})

            # -------------【BOM Mail】-------------------
            subject = f"【BOM Mail】{i.dept}|{i.proj},项目号:{prtno},PCB:{i.pcbpn},样制数量:{i.qty}pcs"
            to = [
                i.bom_owner,
                i.pm,
                i.ee,
                i.me,
                i.mag,
                nt_name,
                "mona.zhang",
                "kuangyi.gu",
                "danfeng.chen",
            ]
            to = {i for i in to if i != "NA"}
            to = ";".join(f"{i}@deltaww.com" for i in to)
            dfb = pd.DataFrame([data])
            dfb = dfb.reindex(
                columns=[
                    "dept",
                    "pm",
                    "ee",
                    "me",
                    "mag",
                    "layout",
                    "proj",
                    "board",
                    "pcbpn",
                    "pcbstatus",
                    "qty",
                ]
            )
            dfb.columns = dfb.columns.str.upper()
            body_table = dfb.to_html(index=False)
            body = f"""
                <B>Dear All:
                <Font Color=red><strong style=background:yellow>
                <p>1.依此邮件基础上全部回复，并提供BOM表；
                <p>2.原则上只回复此项目号相关的BOM； 若BOM含其它机种， 请同时提供其它项目号；
                <p>3.项目号:{prtno}
                {body_table}
                """
            bg_mail(to, subject, body)

            # -------------【PCB Mail】-----------------
            if i.area in ("HZ", "WH"):
                if i.dept_id != 21:
                    subject = f"【PCB Mail】{i.dept}|{i.proj},项目号:{prtno},PCB:{i.pcbpn}-{i.pcbstatus},样制数量:{i.qty}pcs"
                    to = [i.layout, i.ee, i.me, i.pm, nt_name] + cfg.pcb_mail
                    if i.dept_id == 22:
                        to = to + cfg.ape_layout
                    to = ";".join(f"{i}@deltaww.com" for i in to)
                    dfb = pd.DataFrame(
                        [{}],
                        columns=[
                            "机种",
                            "PWB料号",
                            "工程师",
                            "描述",
                            "数量(PCS)",
                            "是否雙面SMD連片數量",
                            "样制",
                            "复投",
                            "是否加急",
                        ],
                    )
                    dfb["描述"] = (
                        "Eg:4層板 T=0mm（板厚） 3OZ（銅厚） OSP（表面處理） FR-4（板材）0*0（連片尺寸）"
                    )
                    dfb["是否雙面SMD連片數量"] = "Eg:2S2P"
                    dfb = dfb.fillna("")
                    body_table = dfb.to_html(index=False)
                    body = f"""
                        <B>Dear {i.layout}:
                        <Font Color=red><strong style=background:yellow>
                        <p>1.请勿修改主题
                        <p>2.出图后，请在此邮件上全部回复，并附上买板资料(Gerber,PNP,连片图)
                        <p>3.如您非该板Layout，请将邮件转发给对应Layout，谢谢
                        {body_table}
                        """
                    bg_mail(to, subject, body)
    return notice(), dfx.to_dict(orient="records")


@callback(
    Output("global-notice", "children"),
    Output(id("project-modify-table"), "rowData"),
    Input(id("project-modify-submit"), "n_clicks"),
    State(id("project-modify-table"), "rowData"),
    State("user", "data"),
)
def project_modify_submit(n_clicks, data, user):
    if not n_clicks:
        raise PreventUpdate
    df = pd.DataFrame(data)
    if "action" not in df.columns:
        return notice("请选择Action", "error"), no_update
    c1 = df["action"] == "update"
    c2 = df["qty"] != df["sm_qty"]
    dfu = df.loc[c1 & c2]

    if dfu.empty:
        return notice("请选择需要修改的记录", "error"), no_update

    dfx = df.loc[~df.index.isin(dfu.index)]

    with pool.connection() as conn:
        with conn.cursor() as cu:
            for i in dfu.itertuples():
                sql = "update ssp.prt set smd_area=%s,dip_area=%s,qty=%s,\
                    smstatus=if(%s,smstatus,%s),findate_sch=concat_ws(',',findate_sch,%s) \
                    where id=%s"
                params = [
                    i.smd_area,
                    i.dip_area,
                    i.qty,
                    i.qty,
                    "Cancel",
                    f"数量{i.sm_qty}>{i.qty}",
                    i.id,
                ]
                cu.execute(sql, params)
                sql = "select source,status from ssp.bom_record where prt_id=%s"
                cu.execute(sql, [i.id])
                res = cu.fetchall()

                if res:
                    bom = [i for i in res if i["source"].lower() == "bom"]
                    if bom:  # * 已经上传BOM
                        change = [
                            i
                            for i in res
                            if ("change" in i["source"].lower())
                            and (i["status"].lower() == "processing")
                        ]
                        if not change:  # * 但是没有change类型在途中
                            data = {
                                "prtno": i.prtno,
                                "prt_id": i.id,
                                "bomtype": "change_qty",
                                "status": "processing",
                                "source": "change",
                                "owner1": i.bom_owner,
                                "processingmode": "4",
                                "update_date": datetime.now(),
                                "receive_date": datetime.now(),
                            }
                            db_insert("ssp.bom_record", data, cu)
            conn.commit()

    subject = "【项目调整通知】"
    nt_name = user.get("nt_name")
    to = [nt_name] + dfu["bom_owner"].unique().tolist() + cfg.sh_mail
    to = ";".join(f"{i}@deltaww.com" for i in to)
    col = [
        "smstatus",
        "area",
        "prtno",
        "dept",
        "proj",
        "smd_area",
        "dip_area",
        "b_ee",
        "b_me",
        "b_mag",
        "bom_owner",
        "sm_qty",
        "qty",
    ]
    dfu = dfu.reindex(columns=col).rename(
        columns={"qty": "调整后数量", "sm_qty": "调整前数量"}
    )
    body = dfu.to_html(index=False)
    bg_mail(to, subject, body)
    return notice(), dfx.to_dict(orient="records")


@callback(
    Output(id("dip-table-left"), "rowData"),
    Output(id("dip-table-right"), "rowData"),
    Output(id("dip-execute"), "children"),
    Output(id("dip-progress"), "children"),
    Output(id("dip-new-btn"), "disabled"),
    Output(id("dip-release-btn"), "disabled"),
    Input(id("dip-tabs"), "value"),
    Input(id("dip-date-picker"), "value"),
    State("user", "data"),
    prevent_initial_call=False,
)
def dip_tabs_active(value, date, user):
    if value == "1":
        left, right = dip_plan_data(date, user)
        return left, right, no_update, no_update, False, False

    elif value == "2":
        return no_update, no_update, dip_execute(date, user), no_update, True, True

    elif value == "3":
        return no_update, no_update, no_update, dip_progress(date), True, True

    else:
        raise PreventUpdate


# @callback(
#     Output(id("dip-table"), "rowTransaction"),
#     Input(id("dip-transfer"), "moveDirection"),
#     Input(id("dip-transfer"), "moveKeys"),
# )
# def dip_transfer_move(moveDirection, moveKeys):
#     if not moveDirection or not moveKeys:
#         raise PreventUpdate

#     if moveDirection == "right":
#         df0 = pd.DataFrame(
#             [i.split("-") for i in moveKeys], columns=["prt_id", "index"]
#         ).astype(str)
#         sql = "select id as prt_id,prtno,proj,ee,me,pm,dept,dip_std_time as total_hour,\
#             qty as total_qty from ssp.prt where id in %s"
#         params = [df0["prt_id"].tolist()]
#         df = read_sql(sql, params=params)
#         df["prt_id"] = df["prt_id"].astype(str)
#         df = df.merge(df0, on="prt_id", how="left")
#         df["uid"] = df["prt_id"] + "-" + df["index"]

#         df.columns = df.columns.str.lower()
#         df["demand_date"] = datetime.now().date()
#         df["standard_hour"] = df["total_hour"]
#         df["offset_hour"] = 0
#         return {"add": df.to_dict(orient="records")}
#     else:
#         return {"remove": [{"uid": key} for key in moveKeys]}


@callback(
    Output(id("dip-popup-card"), "visible"),
    Output(id("dip-popup-card"), "children"),
    Input(id("dip-table-left"), "cellClicked"),
    State(id("dip-table-left"), "rowData"),
)
def dip_popup_card(cell_clicked, row_data):
    if not cell_clicked:
        raise PreventUpdate
    colId = cell_clicked["colId"]
    if colId != "prtno":
        raise PreventUpdate
    row_id = cell_clicked["rowId"]
    data: dict = [i for i in row_data if i["uid"] == row_id][0]
    children = fac.Descriptions(
        [
            fac.DescriptionItem(j, label=i)
            for i, j in data.items()
            if i in ("dip_remark", "project", "dept", "ee", "smtstadate", "fsdate_sch")
        ],
        column=1,
        # layout="vertical",
        bordered=True,
    )
    return True, children


@callback(Input(id("dip-table-left"), "cellValueChanged"))
def dip_left_table_update_remark(changed):
    if not changed:
        raise PreventUpdate
    changed = changed[0]

    colId = changed["colId"]
    if colId != "dip_remark":
        raise PreventUpdate
    data = changed["data"]
    db.update("ssp.prt", {"id": data["prt_id"], "dip_remark": data["dip_remark"]})


@callback(
    Output(id("dip-table-right"), "rowTransaction"),
    Input(id("dip-table-right"), "cellValueChanged"),
)
def dip_right_table_update_batch_hour(changed):
    if not changed:
        raise PreventUpdate
    changed = changed[0]
    colId = changed["colId"]
    if colId != "batch_qty":
        raise PreventUpdate
    data = changed["data"]
    if "each_hour" not in data:
        data["each_hour"] = (data["batch_hour"] + data["offset_hour"]) / changed[
            "oldValue"
        ]
        data["offset_hour"] = 0
    if data["each_hour"] > 0:
        data["batch_hour"] = data["each_hour"] * data["batch_qty"]
    else:
        data["batch_hour"] = (data["standard_hour"] * data["batch_qty"]) / data[
            "total_qty"
        ]
    return {"update": [data]}


# @callback(
#     Output(id("dip-timeline"), "figure"),
#     Input(id("dip-table"), "cellValueChanged"),
#     Input(id("dip-table"), "virtualRowData"),
# )
# def dip_table_to_timeline(cellValueChanged, data):
#     if not cellValueChanged or not data:
#         raise PreventUpdate
#     df = pd.DataFrame(data)
#     if df.columns.intersection(["demand_date", "owner", "standard_hour"]).size < 3:
#         raise PreventUpdate
#     df = df.loc[
#         df["demand_date"].notna() & df["owner"].notna() & df["standard_hour"].notna()
#     ]
#     df["demand_date"] = pd.to_datetime(df["demand_date"])
#     df["standard_hour"] = df["standard_hour"] + df["offset_hour"]
#     df["standard_hour"] = pd.to_timedelta(df["standard_hour"], unit="h")
#     df["standard_hour"] = df.groupby("owner")["standard_hour"].transform("cumsum")
#     df["start"] = df["demand_date"] + pd.Timedelta(hours=9)
#     df["end"] = df["start"] + df["standard_hour"]

#     df["shifted"] = df.groupby("owner")["end"].transform(lambda x: x.shift(1))
#     df["start"] = np.where(df["shifted"].notna(), df["shifted"], df["start"])
#     fig = px.timeline(
#         df,
#         x_start="start",
#         x_end="end",
#         y="owner",
#         color="prtno",
#         text="prtno",
#         template="simple_white",
#     )
#     fig.update_traces(insidetextanchor="middle")
#     return fig


# clientside_callback(
#     ClientsideFunction(namespace="gantt", function_name="initGantt"),
#     Output("gantt-container", "children"),
#     Input("gantt-container", "id"),
#     prevent_initial_call=False,
# )


# @callback(
#     Output("1", "value"),
#     Output("2", "value"),
#     Output("3", "value"),
#     Input(id("interval"), "n_intervals"),
#     State("1", "value"),
#     State("2", "value"),
#     State("3", "value"),
# )
# def send_new_notification(n_intervals, v1, v2, v3):
#     if not n_intervals:
#         raise PreventUpdate
#     return v1 + n_intervals, v2 + n_intervals, v3 + n_intervals


@callback(
    Output(id("grid"), "layout"),
    Output(id("grid"), "children"),
    Input(id("dip-transfer1"), "moveDirection"),
    Input(id("dip-transfer1"), "moveKeys"),
    State(id("grid"), "layout"),
    State(id("grid"), "children"),
)
def grid_layout_update(moveDirection, moveKeys, layout, children):
    colors = [
        "#64B5F6",  # 清新蓝
        "#81C784",  # 清新绿
        "#FFB74D",  # 温暖橙
        "#BA68C8",  # 优雅紫
        "#4FC3F7",  # 天空蓝
        "#AED581",  # 嫩叶绿
        "#FF8A65",  # 珊瑚色
        "#9575CD",  # 梦幻紫
        "#4DD0E1",  # 碧蓝色
        "#DCE775",  # 柠檬黄
    ]

    used_colors = [
        i["props"]["children"]["props"]["color"]
        for i in children
        if i["props"]["id"].isdigit()
    ]
    colors = [i for i in colors if i not in used_colors]

    if not moveDirection or not moveKeys:
        raise PreventUpdate
    if moveDirection == "right":
        for i, j in enumerate(moveKeys, start=1):
            l = {
                "w": 2,
                "h": 1,
                "x": i,
                "y": 0,
                "i": f"{j}",
                "minW": 1,
                "minH": 1,
                "maxH": 1,
                "maxW": 11,
            }
            layout.append(l)
            sql = "select dept,prtno,proj,qty,express_date,express_qty,dip_std_time \
                from ssp.prt where id=%s"
            params = [j]
            prt = db.execute_fetchone(sql, params)

            p = fac.Popover(
                dmc.Progress(
                    value=100, label=prt["prtno"], h="100%", color=colors[i], size=20
                ),
                title=dmc.Group(
                    [
                        dmc.NumberInput(
                            value=prt["qty"],
                            min=1,
                            max=prt["qty"],
                            id={"type": id("batch-qty"), "index": f"{j}"},
                            size="xs",
                            debounce=1000,
                            label="本批次数量",
                        ),
                        dmc.NumberInput(
                            value=prt["dip_std_time"],
                            id={"type": id("batch-std-time"), "index": f"{j}"},
                            label="本批次工时",
                            disabled=True,
                            precision=1,
                            size="xs",
                        ),
                    ]
                ),
                content=fac.Descriptions(
                    [
                        fac.DescriptionItem(prt["dept"], label="部门"),
                        fac.DescriptionItem(
                            prt["qty"],
                            label="数量",
                            id={"type": id("total-qty"), "index": f"{j}"},
                        ),
                        fac.DescriptionItem(
                            prt["dip_std_time"],
                            label="标准工时",
                            id={"type": id("total-std-time"), "index": f"{j}"},
                        ),
                        fac.DescriptionItem(prt["express_date"], label="寄出日期"),
                        fac.DescriptionItem(prt["express_qty"], label="寄出数量"),
                    ],
                    layout="vertical",
                    title=prt["proj"],
                    bordered=True,
                    column=3,
                ),
                id=f"{j}",
            )
            children.append(p)
    elif moveDirection == "left":
        for i, j in enumerate(moveKeys):
            layout = [i for i in layout if i["i"] != f"{j}"]
            children = [i for i in children if i["props"]["id"] != f"{j}"]
    return layout, children


# @callback(
#     Output(id("first-load-store"), "data"),
#     Input({"type": id("batch-qty"), "index": ALL}, "value"),
# )
# def batch_std_time(batch_qty):
#     if not batch_qty:
#         raise PreventUpdate
#     return False


@callback(
    Output({"type": id("batch-std-time"), "index": MATCH}, "value"),
    Input({"type": id("batch-qty"), "index": MATCH}, "value"),
    State({"type": id("total-qty"), "index": MATCH}, "children"),
    State({"type": id("total-std-time"), "index": MATCH}, "children"),
)
def batch_std_time(batch_qty, total_qty, total_std_time):
    if not batch_qty:
        raise PreventUpdate
    std_batch = float(total_std_time) * batch_qty / int(total_qty)
    return std_batch


@callback(
    Output(id("grid"), "layout"),
    Output(id("first-load-store"), "data"),
    Input({"type": id("batch-std-time"), "index": ALL}, "value"),
    State(id("grid"), "layout"),
    State(id("first-load-store"), "data"),
)
def batch_qty_update_layout(value, layout, first_load):
    if first_load:
        return no_update, False

    if not value:
        raise PreventUpdate

    tid = ctx.triggered_id["index"]
    if not tid:
        raise PreventUpdate

    idx = [i for i, j in enumerate(ctx.inputs_list[0]) if j["id"]["index"] == tid][0]
    for i in layout:
        if i["i"] == f"{tid}":
            if value[idx] > 11:
                i["w"] = 11
            else:
                i["w"] = value[idx]
    return layout, False


# @callback(
#     Output(id("dip-table"), "rowData"),
#     Input(id("dip-date-picker"), "value"),
#     State("user", "data"),
#     prevent_initial_call=False,
# )
# def load_dip_plan(dip_date, user):
#     if not dip_date:
#         raise PreventUpdate
#     sql = "select a.id,a.uid,a.qty,standard_hour,offset_hour,owner,\
#         b.prtno,b.smtfin_date,proj,ee,me,pm,dept,\
#         dip_std_time as total_hour,b.qty as total_qty \
#         from ssp.prt_dip a \
#         left join ssp.prt b on a.prt_id=b.id \
#         where plan_date=%s and planner=%s"
#     params = [dip_date, user.get("nt_name")]
#     res = db.execute(sql, params)
#     if res:
#         return res
#     else:
#         raise PreventUpdate


@callback(
    Output("msg", "children"),
    Output(id("dip-tabs"), "value"),
    Input(id("dip-release-btn"), "nClicks"),
    State(id("dip-date-picker"), "value"),
    State(id("dip-table-right"), "virtualRowData"),
    State("user", "data"),
)
def dip_release_plan(nClicks, dip_date, data, user):
    if not nClicks:
        raise PreventUpdate

    if not nClicks or not dip_date or not data:
        raise PreventUpdate

    if not all(i.get("owner") for i in data):
        return fac.Message(content="负责人不能为空", type="error"), no_update
    if not all(i.get("batch_qty") for i in data):
        return fac.Message(content="批量不能为空", type="error"), no_update

    for i in data:
        batch_qty = i.get("batch_qty")
        owner = i.get("owner")
        prt_id = i.get("prt_id")
        _id = i.get("id")
        if batch_qty:
            if prt_id:
                if i.get("table") == "delivery":
                    db.insert(
                        "ssp.prt_delivery",
                        {
                            "plan_date": dip_date,
                            "owner": owner,
                            "prt_id": i.get("prt_id"),
                            "batch_qty": batch_qty,
                            "batch_hour": i.get("batch_hour"),
                            "offset_hour": i.get("offset_hour") or 0,
                            "planner": user.get("nt_name"),
                            "uid": i.get("uid"),
                            "prtno": i.get("prtno"),
                            "remark": i.get("dip_remark"),
                            "dip_area": i.get("dip_area"),
                        },
                    )
                else:
                    db.insert(
                        "ssp.prt_dip",
                        {
                            "plan_date": dip_date,
                            "owner": owner,
                            "prt_id": i.get("prt_id"),
                            "batch_qty": batch_qty,
                            "batch_hour": i.get("batch_hour"),
                            "offset_hour": i.get("offset_hour") or 0,
                            "planner": user.get("nt_name"),
                            "uid": i.get("uid"),
                            "prtno": i.get("prtno"),
                            "remark": i.get("dip_remark"),
                            "fsdate_sch": i.get("fsdate_sch"),
                            "urgent": i.get("urgent"),
                            "dip_area": i.get("dip_area"),
                        },
                    )
            if _id:
                if i.get("table") == "delivery":
                    db.update(
                        "ssp.prt_delivery",
                        {
                            "id": _id,
                            "owner": owner,
                            "batch_qty": batch_qty,
                            "batch_hour": i.get("batch_hour"),
                            "offset_hour": i.get("offset_hour") or 0,
                            "remark": i.get("dip_remark"),
                            "urgent": i.get("urgent"),
                        },
                    )
                else:
                    db.update(
                        "ssp.prt_dip",
                        {
                            "id": _id,
                            "owner": owner,
                            "batch_qty": batch_qty,
                            "batch_hour": i.get("batch_hour"),
                            "offset_hour": i.get("offset_hour") or 0,
                            "remark": i.get("dip_remark"),
                            "urgent": i.get("urgent"),
                        },
                    )
    return fac.Message(content="计划发布成功", type="success"), "1"


@callback(
    Output(id("dip-loading"), "value"),
    Input(id("dip-table-right"), "rowData"),
    Input(id("dip-table-right"), "cellValueChanged"),
    State("user", "data"),
)
def dip_loading(data, cellValueChanged, user):
    if not data:
        return 0
    x = [i.get("batch_hour") + i.get("offset_hour") for i in data]
    if not x:
        return 0
    x = sum(x)
    area = user.get("area")
    if area == "SH":
        y = 6.5 * 5
    elif area == "HZ":
        y = 6.5 * 3.4
    elif area == "WH":
        y = 6.5 * 2.4
    else:
        y = 6.5 * 5
    # owner = set([i.get("owner") for i in data])
    # y = sum(6.5 * 0.4 if i in ("Lili.Fang", "Fanwei.Xie") else 6.5 for i in owner)
    loading = x * 100 / y
    return f"{x:.1f}/{y:.1f}={loading:.0f}"


@callback(
    Output(id("dip-new-modal"), "visible"),
    Output(id("dip-new-rd"), "options"),
    Input(id("dip-new-btn"), "nClicks"),
)
def dip_display_modal(nClicks):
    if not nClicks:
        raise PreventUpdate
    sql = "select nt_name from ssp.user"
    df = read_sql(sql)
    options = df["nt_name"].str.title().tolist()
    return True, options


@callback(
    Output(id("dip-new-modal"), "okButtonProps"),
    Input(id("dip-new-rd"), "value"),
    Input(id("dip-new-proj"), "value"),
    Input(id("dip-new-qty"), "value"),
    Input(id("dip-new-hour"), "value"),
)
def dip_new_modal_ok_button_props(rd, proj, qty, hour):
    if all([rd, proj, qty, hour]):
        return {"disabled": False}
    else:
        return {"disabled": True}


@callback(
    Output("msg", "children"),
    Output(id("dip-tabs"), "value"),
    Input(id("dip-new-modal"), "okCounts"),
    State(id("dip-new-rd"), "value"),
    State(id("dip-new-proj"), "value"),
    State(id("dip-new-qty"), "value"),
    State(id("dip-new-hour"), "value"),
    State("user", "data"),
)
def dip_new_modal_submit(ok, rd, proj, qty, hour, user):
    if not ok:
        raise PreventUpdate

    sql = "select area,dept,dept_id from ssp.user where nt_name=%s"
    params = [rd]
    res = db.execute_fetchone(sql, params)
    rd_dept = res.get("dept")
    rd_dept_id = res.get("dept_id")

    nt_name = user.get("nt_name")
    area = user.get("area")

    sql = "SELECT RIGHT(PrtNo,7) as maxid FROM ssp.prt \
    where PrtNo like %s ORDER BY id DESC limit 1"
    params = [f"{area}%"]
    res = db.execute_fetchone(sql, params)
    smd_area = area[0]
    dip_area = area[0]
    prtno = f"{area}{smd_area}{dip_area}{int(res['maxid']) + 1}"
    now = datetime.now()
    db.insert(
        "ssp.prt",
        {
            "project_id": 10000,
            "stage": 0,
            "board": proj,
            "prtno": prtno,
            "proj": proj,
            "pcbpn": "重工",
            "qty": qty,
            "SMStatus": "SMTFINISH",
            "pcbstatus": now,
            "Dept": rd_dept,
            "dept_id": rd_dept_id,
            "EE": rd,
            "PM": nt_name,
            "AppDate": now,
            "FSQty": qty,
            "FSDate_Req": now,
            "Area": area,
            "b_ee": "X",
            "smd_area": area,
            "dip_area": area,
            "dip_std_time": hour,
            "SMTStaDate": now,
            "SMTFin_Date": now,
            "placer": "WAVE",
            "pcb_release_date": now,
        },
    )
    return fac.Message(content="新增计划成功", type="success"), "1"


@callback(
    Output({"type": id("start"), "index": MATCH}, "disabled"),
    Output({"type": id("end"), "index": MATCH}, "disabled"),
    Output({"type": id("countdown"), "index": MATCH}, "value"),
    Input({"type": id("start"), "index": MATCH}, "nClicks"),
    State({"type": id("countdown"), "index": MATCH}, "key"),
)
def dip_execute_start_disable(nClicks, hours):
    if not nClicks:
        raise PreventUpdate
    idx = ctx.triggered_id.get("index")
    now = datetime.now()
    db.update("ssp.prt_dip", {"id": idx, "start_time": now})
    deadline = (now + timedelta(hours=hours)).strftime("%Y-%m-%d %H:%M:%S:%f")
    return True, False, deadline


@callback(
    Output({"type": id("end"), "index": MATCH}, "disabled"),
    Input({"type": id("end"), "index": MATCH}, "nClicks"),
    State("user", "data"),
)
def dip_execute_end_disable(nClicks, user):
    if not nClicks:
        raise PreventUpdate
    _id = ctx.triggered_id.get("index")
    sql = "select * from ssp.prt_dip where prt_id=(select prt_id from ssp.prt_dip where id=%s)"
    res = db.execute(sql, [_id])
    finish_qty = sum([i.get("batch_qty") for i in res])
    res0 = [i for i in res if i.get("id") == int(_id)][0]
    prt_id = res0.get("prt_id")
    fsdate_sch: datetime = res0.get("fsdate_sch")
    now = datetime.now()
    sql = "select qty,ee,pm,prtno,proj from ssp.prt where id=%s"
    res1 = db.execute_fetchone(sql, [prt_id])
    total_qty = res1.get("qty")
    prtno = res1.get("prtno")
    proj = res1.get("proj")
    nt_name = user.get("nt_name")
    area = user.get("area")

    db.update("ssp.prt_dip", {"id": _id, "end_time": now})

    if len(res) == 1:
        db.update(
            "ssp.prt",
            {
                "id": prt_id,
                "smstatus": "FSFINISH",
                "fsdate_act": now,
                "FSQty": res0.get("batch_qty"),
                "DIPStaDate": res0.get("start_time"),
            },
        )
        ee = res1.get("ee")
        pm = res1.get("pm")
        to = [nt_name, ee, pm]
        if area == "HZ":
            to.append("weiming.li")
        to = ";".join(f"{i}@deltaww.com" for i in to)
        subject = f"【样制完成通知】{prtno}共{total_qty}台,完成{finish_qty}台,请至样制间取走样品"
        body = proj
        bg_mail(to, subject, body)
        if now.date() > fsdate_sch.date():
            set_props(id("dip-delay-store"), {"data": {"prt_id": prt_id, "id": _id}})
            set_props(id("dip-delay-modal"), {"visible": True})
        else:
            set_props(id("dip-tabs"), {"value": "2"})
    else:
        set_props(id("dip-tabs"), {"value": "2"})

    if finish_qty >= total_qty:
        db.update(
            "ssp.prt",
            {
                "id": prt_id,
                "smstatus": "Close",
                "FinDate_Act": now,
            },
        )

    return True


@callback(
    Output({"type": id("delivery"), "index": MATCH}, "disabled"),
    Input({"type": id("delivery"), "index": MATCH}, "nClicks"),
    Input({"type": id("delivery"), "index": MATCH}, "key"),
)
def dip_delivery_btn(nClicks, prt_id):
    if not nClicks:
        raise PreventUpdate
    _id = ctx.triggered_id.get("index")
    set_props(id("dip-delivery-modal"), {"visible": True})
    set_props(id("dip-delivery-store"), {"data": {"id": _id, "prt_id": prt_id}})
    return True


@callback(
    Output(id("dip-delivery-modal"), "visible"),
    Input(id("dip-delivery-modal"), "okCounts"),
    State(id("dip-delivery-store"), "data"),
    State(id("dip-delivery-input"), "value"),
)
def dip_delivery_modal_submit(nClicks, store, delivery_no):
    if not nClicks or not delivery_no:
        raise PreventUpdate
    now = datetime.now()
    db.update(
        "ssp.prt_delivery",
        {
            "id": store.get("id"),
            "end_time": now,
            "start_time": now,
            "delivery_no": delivery_no,
        },
    )
    sql = "update ssp.prt set dip_remark=CONCAT_WS(',',dip_remark,%s) where id=%s"
    params = [f"{now:%m/%d}:{delivery_no}", store.get("prt_id")]
    db.execute(sql, params)
    set_props(id("dip-tabs"), {"value": "2"})
    return True


@callback(
    Output(id("dip-delay-modal"), "visible"),
    Input(id("dip-delay-modal"), "okCounts"),
    State(id("dip-delay-reason"), "value"),
    State(id("dip-delay-store"), "data"),
    State("user", "data"),
)
def dip_delay_modal(ok, reason, store, user):
    if not ok or not reason:
        raise PreventUpdate

    nt_name = user.get("nt_name").title()
    sql = "update ssp.prt set delay_reason=%s,\
        findate_sch=CONCAT_WS(',',findate_sch,%s) where id=%s"
    params = [reason, f"({nt_name}:超期原因:{reason})", store.get("prt_id")]
    db.execute(sql, params)

    sql = "update ssp.prt_dip set delay_reason=%s where id=%s"
    params = [reason, store.get("id")]
    db.execute(sql, params)
    set_props(id("dip-tabs"), {"value": "2"})
    return False


@callback(
    Output(id("smd-loading"), "value"),
    Input(id("smd-table-right"), "virtualRowData"),
)
def smd_loading(data):
    if not data:
        raise PreventUpdate
    loading = (sum(i["leadtime"] for i in data) / 6.5) * 100
    return round(loading, 0)


@callback(
    Output("msg", "children"),
    Input(id("smd-release-btn"), "nClicks"),
    State(id("smd-table-right"), "virtualRowData"),
    State(id("smd-date-picker"), "value"),
    State("user", "data"),
)
def smd_release(nClicks, data, date, user):
    if not nClicks:
        raise PreventUpdate
    if not data:
        raise PreventUpdate
    nt_name = user.get("nt_name")
    sql = "delete from ssp.prt_smd where plan_date=%s and planner=%s"
    db.execute(sql, (date, nt_name))

    for i in data:
        db.insert(
            "ssp.prt_smd",
            {
                "plan_date": date,
                "prt_id": i.get("id"),
                "planner": nt_name,
            },
        )
    return fac.Message(content="计划发布成功", type="success")


@callback(
    Output(id("ai-result"), "children"),
    Input(id("ai-modal"), "okCounts"),
    State(id("ai-prompt"), "children"),
    State(id("smd-table-left"), "rowData"),
    running=[
        (Output(id("ai-modal"), "confirmLoading"), True, False),
    ],
)
def ai_result(okCounts, prompt, data):
    if not okCounts:
        raise PreventUpdate
    df = pd.DataFrame(data)
    res = smd_plan({"prompt": prompt})["result"]
    analysis = res.analysis
    schedule = [i.model_dump() for i in res.schedule]
    df1 = pd.DataFrame(schedule)

    df = df[["prtno", "leadtime", "earliest_start_date", "latest_start_date"]].merge(
        df1[["project", "placer", "start_time", "end_time"]],
        left_on="prtno",
        right_on="project",
        how="left",
    )
    df["plan_hour"] = df["end_time"] - df["start_time"]
    df["plan_hour"] = df["plan_hour"].dt.total_seconds() / 3600
    df["plan_hour"] = df["plan_hour"].round(1)
    df["c1"] = df["start_time"] < df["earliest_start_date"]
    df["c2"] = df["plan_hour"] < df["leadtime"]

    table = dag.AgGrid(
        className="ag-theme-alpine compact",
        rowData=df.to_dict(orient="records"),
        columnDefs=[
            {
                "headerName": "项目号",
                "field": "project",
                "width": 125,
                "pinned": "left",
                "cellStyle": {"padding": 1},
                "rowDrag": True,
            },
            {
                "headerName": "设备",
                "field": "placer",
                "width": 60,
                "cellStyle": {"padding": 1},
            },
            {
                "headerName": "工时",
                "field": "leadtime",
                "width": 60,
                "cellStyle": {"padding": 1},
            },
            {
                "headerName": "最早可开始",
                "field": "earliest_start_date",
                "width": 60,
                "valueFormatter": {
                    "function": "d3.timeFormat('%m/%d')(d3.isoParse(params.value))"
                },
                "cellStyle": {"padding": 1},
            },
            {
                "headerName": "最晚需开始",
                "field": "latest_start_date",
                "width": 60,
                "valueFormatter": {
                    "function": "d3.timeFormat('%m/%d')(d3.isoParse(params.value))"
                },
                "cellStyle": {"padding": 1},
            },
            {
                "headerName": "计划开始",
                "field": "start_time",
                "width": 60,
                "valueFormatter": {
                    "function": "d3.timeFormat('%m/%d')(d3.isoParse(params.value))"
                },
                "cellStyle": {"padding": 1},
            },
            {
                "headerName": "计划结束",
                "field": "end_time",
                "width": 60,
                "valueFormatter": {
                    "function": "d3.timeFormat('%m/%d')(d3.isoParse(params.value))"
                },
                "cellStyle": {"padding": 1},
            },
            {
                "headerName": "计划工时",
                "field": "plan_hour",
                "width": 60,
                "cellStyle": {"padding": 1},
            },
            {
                "headerName": "计划开始小于最早可开始",
                "field": "c1",
                "width": 60,
                "cellStyle": {"padding": 1},
            },
            {
                "headerName": "计划工时小于贴片用时",
                "field": "c2",
                "width": 60,
                "cellStyle": {"padding": 1},
            },
        ],
        defaultColDef={
            "resizable": True,
            "sortable": True,
            "filter": True,
            "wrapHeaderText": True,
            "autoHeaderHeight": True,
        },
        dashGridOptions={
            # "rowSelection": "single",
            # "stopEditingWhenCellsLoseFocus": True,
            # "singleClickEdit": True,
            "animateRows": "animate",
            # "enableCellTextSelection": True,
            # "enableBrowserTooltips": True,
            # "tooltipInteraction": True,
            # "tooltipMouseTrack": True,
            # "tooltipShowDelay": 200,
            "rowDragManaged": True,
            "rowDragEntireRow": True,
            # "rowDragMultiRow": True,
            "suppressMoveWhenRowDragging": True,
        },
        style={"height": "80vh"},
        getRowId="params.data.id",
    )

    fig = px.timeline(
        schedule, x_start="start_time", x_end="end_time", y="placer", color="project"
    )

    return fac.Flex(
        [
            dcc.Markdown(analysis),
            dcc.Graph(figure=fig),
            table,
            fac.Space(
                [
                    fac.Title(f"无法排产的项目{len(res.unplanned)}", level=5),
                    fac.Text(res.unplanned),
                ],
                direction="vertical",
            ),
        ],
        vertical=True,
    )


# @callback(
# def dip_execute_start(data, cellValueChanged, user):

# @callback(
#     Input(id("dip-progress-graph"), "clickData"),
# )
# def dip_progress_graph_clickData(clickData):
#     print(clickData)

clientside_callback(
    ClientsideFunction("addDropZone", "dropZoneGrid2GridSimple"),
    Output(id("smd-table-left"), "id"),
    Input(id("smd-table-left"), "id"),
    State(id("smd-table-right"), "id"),
)
# clientside_callback(
#     ClientsideFunction("addDropZone", "dropZoneGrid2GridComplex"),
#     Output(id("dip-table-left"), "id"),
#     Input(id("dip-table-left"), "id"),
#     State(id("dip-table-left"), "id"),
#     State(id("dip-table-right"), "id"),
#     prevent_initial_call=False,
# )


# @callback(
#     Output(id("grid"), "layout"),
#     Output(id("grid"), "children"),
#     Output(id("first-load-store"), "data"),
#     Input(id("dip-date-picker"), "value"),
#     State("user", "data"),
#     prevent_initial_call=False,
# )
# def load_dip_plan1(dip_date, user):
#     if not dip_date:
#         raise PreventUpdate
#     res = db.find_one(
#         "ssp.prt_dip_layout",
#         {"date": dip_date, "owner": user.get("nt_name")},
#     )
#     if res:
#         layout = loads(res.get("layout"))
#         children = loads(res.get("children"))
#         return layout, children, True
#     else:
#         layout = cfg.hz_dip
#         children = [
#             dmc.Avatar(i["i"].title(), id=i["i"], w="100%", color="blue")
#             for i in layout
#         ]
#         return layout, children, False


# @callback(
#     Output(id("dip-process"), "percent"),
#     Input(id("grid"), "layout"),
# )
# def dip_process(layout):
#     x = [i["w"] for i in layout if i["i"].isdigit()]
#     if not x:
#         return 0
#     loading = sum(x) * 100 / (6.5 * 4)
#     return int(loading)


# @callback(
#     Output(id("grid"), "layout"),
#     Output(id("grid"), "children"),
#     Input(id("dip-date-picker"), "value"),
#     Input(id("dip-store"), "data"),
#     State(id("grid"), "layout"),
#     State(id("grid"), "children"),
#     prevent_initial_call=False,
# )
# def dip_store(value, data, layout, children):
#     if not value or not data:
#         raise PreventUpdate
#     print(value)
#     data = data.get(value)
#     if not data:
#         raise PreventUpdate
#     return data["layout"], data["children"]


# @callback(
#     Output("web-notification", "message"),
#     Input(id("interval"), "n_intervals"),
# )
# def send_new_notification(n_intervals):
#     if not n_intervals:
#         raise PreventUpdate
#     return "有新的插件需求，请及时处理"
