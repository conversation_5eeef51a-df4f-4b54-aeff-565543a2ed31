import instructor
from pydantic import BaseModel, Field
from openai import OpenAI
import os
from datetime import datetime
from enum import Enum
# from pydantic_ai import Agent
# from pydantic_ai.models.openai import OpenAIModel
# from pydantic_ai.providers.openai import OpenAIProvider
# from pydantic_ai.providers.openrouter import OpenRouterProvider


class PlacerType(str, Enum):
    MY300 = "MY300"
    BM221 = "BM221"
    W2 = "W2"


# Define your desired output structure
class SmdPlanList(BaseModel):
    project: str = Field(description="项目名称")
    placer: PlacerType = Field(description="贴片设备")
    start_date: datetime = Field(description="计划开始时间")
    end_date: datetime = Field(description="计划完成时间")


class SmdPlan(BaseModel):
    analysis: str = Field(description="分析过程,要求中文")
    schedule: list[SmdPlanList] = Field(description="SMD计划列表")


api_key = os.getenv("OPENROUTER_API_KEY")
base_url = "https://openrouter.ai/api/v1"
model = "qwen/qwen-2.5-72b-instruct"

# api_key = "sk-amvaicfhwwibhnrnwrbudcwzdhwnphkasglgqkydapfmyoav"
# base_url = "https://api.siliconflow.cn/v1"
# model = "Pro/deepseek-ai/DeepSeek-R1"
# model = "Qwen/Qwen3-30B-A3B"

# client.on("completion:kwargs", log_kwargs)
# client.on("completion:error", log_exception)


def call_llm1(prompt):
    client = OpenAI(api_key=api_key, base_url=base_url)
    r = client.chat.completions.create(
        model=model, messages=[{"role": "user", "content": prompt}]
    )
    return r.choices[0].message.content


def call_llm(prompt):
    client = instructor.from_openai(
        OpenAI(api_key=api_key, base_url=base_url),
        mode=instructor.Mode.JSON,
    )
    r = client.chat.completions.create(
        model=model,
        response_model=SmdPlan,
        messages=[{"role": "user", "content": prompt}],
    )
    # dict_list = [item.model_dump() for item in r]
    # print(dict_list)
    return r


# def call_llm2(prompt):
#     model = OpenAIModel(
#         "google/gemini-2.5-flash-preview-05-20",
#         provider=OpenRouterProvider(api_key=os.getenv("OPENROUTER_API_KEY")),
#     )
#     # model = OpenAIModel(
#     #     "deepseek/deepseek-r1-0528:free",
#     #     provider=OpenRouterProvider(api_key=os.getenv("OPENROUTER_API_KEY")),
#     # )
#     # api_key = os.getenv("OPENROUTER_API_KEY")
#     # base_url = "https://openrouter.ai/api/v1"
#     # model = "google/gemini-2.5-flash-preview-05-20"
#     # model = OpenAIModel(
#     #     model,
#     #     provider=OpenAIProvider(base_url=base_url, api_key=api_key),
#     # )
#     agent = Agent(model, output_type=SmdPlan, retries=3)
#     result = agent.run_sync(prompt)
#     return result.output
