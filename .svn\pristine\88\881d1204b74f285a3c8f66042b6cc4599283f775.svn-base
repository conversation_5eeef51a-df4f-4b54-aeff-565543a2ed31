from pocketflow import Node, Flow
from .utils import call_llm


class ChatNode(Node):
    def prep(self, shared):
        return shared["message"]

    def exec(self, message):
        prompt = f"""
        你是生产制造计划安排专家，负责SMD生产计划安排
        充分利用工作时间和设备资源，给出最优计划方案
        结合以下要求，排定如下项目的SMD计划，确定每个项目的开始时间，结束时间,以及设备
        项目清单：
        {message}
        排定要求：
        1.从2025年6月6日开始排计划，排不下排到后面一天，以此类推
        3.不同设备可以独立安排项目，合理分配项目，充分利用好设备
        4.MY300适合生产点数少的项目，BM221适合生产点数多的项目，W2适合生产点数多的项目
        5.早上9点开始，下午4点30下班，这之间的时间可以安排项目
        6.每台设备最多能上120种材料
        7.项目之间半小时间隔切换项目
        8.尽量不要跨天安排项目
        9.尽量不要安排在中午11点30分到13点30分之间，因为要吃饭
        以yaml格式输出，包含analysis和schedule字段,analysis是分析过程，schedule是SMD计划
        示例：
        analysis: "包含大模型的所有分析过程，以及分析结果"
        schedule:
        - prtno: "SHSS2505296"
          placer: "MY300"
          start_date: "2024-03-20T09:00:00"
          end_date: "2024-03-20T17:00:00"
        """
        print(prompt)
        response = call_llm(prompt)
        return response

    def post(self, shared, prep_res, exec_res):
        shared["result"] = exec_res
        return


def smd_plan(shared):
    chat_node = ChatNode()
    flow = Flow(start=chat_node)
    flow.run(shared)
    return shared
