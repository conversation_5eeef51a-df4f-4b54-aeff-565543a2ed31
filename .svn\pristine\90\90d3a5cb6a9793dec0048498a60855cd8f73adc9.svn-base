from datetime import datetime

import dash_ag_grid as dag
import dash_mantine_components as dmc
import feffery_antd_components as fac
import openpyxl
import pandas as pd
import plotly.express as px
from dash import Patch, no_update
from dash.exceptions import PreventUpdate
from dash_extensions.enrich import (
    Input,
    Output,
    State,
    callback,
    ctx,
    dash,
    dash_table,
    dcc,
    html,
)
from dash_iconify import DashIconify

from common import get_ssp_user, id_factory, read_sql
from components import notice
from config import SSP_DIR, UPLOAD_FOLDER_ROOT, cfg, pool
from tasks import bg_mail

colors = px.colors.qualitative.D3

id = id_factory(__name__)

dash.register_page(__name__, title="PCB")

style_header = {
    "backgroundColor": "rgba(52, 73, 94)",
    "color": "white",
    "fontWeight": "bold",
    "fontSize": "12px",
    # 'border': '1px solid',
    "textTransform": "uppercase",
}

style_cell = {
    "whiteSpace": "normal",
    "height": "auto",
    "textAlign": "left",
    "font-family": "Helvetica",
    "font-size": "10px",
}

tab2_content = html.Div(
    [
        html.Br(),
        dash_table.DataTable(
            editable=True,
            id="pcb-query-table",
            style_header=style_header,
            style_cell=style_cell,
            export_format="xlsx",
            filter_action="native",
            filter_options={"case": "insensitive"},
            page_action="native",
            page_current=0,
            page_size=10,
        ),
    ]
)

pcb_table_columns = [
    "board_name",
    "pn",
    "description",
    "size",
    "layer",
    "thickness",
    "copper",
    "layout",
    "qpa",
]


def layout(**kwargs):
    user = get_ssp_user()
    c1 = user["dept_id"].isin([21, 22, 28])
    c2 = user["role_group"] == "PM"
    c3 = user["role_group"] == "EE"
    c4 = user["role_group"] == "ME"
    c5 = user["role_group"] == "MAG"
    pm = user.loc[c1 & c2]["nt_name"].tolist()
    ee = user.loc[c1 & c3]["nt_name"].tolist()
    me = user.loc[c1 & c4]["nt_name"].tolist()
    mag = user.loc[c5]["nt_name"].tolist() + ["NA"]
    with pool.connection() as conn:
        with conn.cursor() as cu:
            cu.execute("select distinct project,model from ssp_ext.nre_project")
            model = [f"{i['project']}-{i['model']}" for i in cu.fetchall()]

    pcb_tree = dmc.Grid(
        [
            dmc.Col(
                dmc.Select(
                    id="pcb-model",
                    label="Model",
                    data=model,
                    searchable=True,
                    # creatable=True,
                ),
                span=6,
            ),
            dmc.Col(
                fac.AntdUpload(
                    id="pcb-upload",
                    apiUrl="/upload/pcbtree/",
                    buttonContent="上传PCB TREE文件",
                    showUploadList=False,
                    showSuccessMessage=True,
                    fileTypes=["xlsx"],
                    disabled=True,
                ),
                span=3,
            ),
            dmc.Col(
                dmc.Button(
                    "PCB TREE标准格式",
                    id="pcbtree-download-btn",
                    variant="subtle",
                    color="orange",
                    rightIcon=DashIconify(icon="material-symbols:download"),
                ),
                span=2,
            ),
            dmc.Col(
                dmc.Button(
                    "教程",
                    id="pcbpn-download-btn",
                    variant="subtle",
                    color="orange",
                    rightIcon=DashIconify(icon="material-symbols:download"),
                ),
                span=1,
            ),
            dmc.Col(
                dmc.Select(id="pcb-pm", label="PM", data=pm, searchable=True),
                span=3,
            ),
            dmc.Col(
                dmc.Select(id="pcb-ee", label="EE", data=ee, searchable=True),
                span=3,
            ),
            dmc.Col(
                dmc.Select(id="pcb-me", label="ME", data=me, searchable=True),
                span=3,
            ),
            dmc.Col(
                dmc.Select(id="pcb-mag", label="MAG", data=mag, searchable=True),
                span=3,
            ),
            dmc.Col(
                dcc.Loading(
                    dag.AgGrid(
                        id="pcb-table",
                        columnDefs=[
                            {"field": i, "headerName": i} for i in pcb_table_columns
                        ],
                        rowData=[],
                        columnSize="sizeToFit",
                        defaultColDef={
                            "resizable": True,
                            "sortable": True,
                            "filter": True,
                            "wrapHeaderText": True,
                            "autoHeaderHeight": True,
                            "editable": True,
                        },
                        dashGridOptions={
                            "rowSelection": "single",
                            "stopEditingWhenCellsLoseFocus": True,
                            "singleClickEdit": True,
                            "rowHeight": 35,
                        },
                        style={"height": 300, "display": "none"},
                    )
                ),
                span=12,
            ),
            dmc.Col(
                fac.AntdPopconfirm(
                    dmc.Button("提交", id="pcb-submit", fullWidth=True),
                    title=dmc.List(
                        [
                            dmc.Title(
                                "请确认下面3个问题都已考虑，再点“确定”走下一步：",
                                order=5,
                                color="red",
                            ),
                            dmc.ListItem(
                                "请确认是否该项目的所有PCB均已列出(包含未变更PCB或者用到的已量产PCB)?"
                            ),
                            dmc.ListItem(
                                "请确认板子的单机用量是否正确(有无PCB是2倍及以上用量)?"
                            ),
                            dmc.ListItem("请确认本次取消的板子已删除?"),
                        ]
                    ),
                    trigger="click",
                    id="pcb-submit-confirm",
                ),
                span=12,
            ),
            dcc.Loading(dcc.Download(id="pcbpn-download"), fullscreen=True),
        ],
        align="end",
        justify="space-between",
    )
    sql = "select a.id,prtno,b.project,a.board,a.pcbpn,a.ee,a.me,a.pm,a.appdate,\
        a.qty,a.dept,a.pcbstatus,a.proj from prt a \
        left join project b on a.project_id=b.id \
        where pcb_release_date is null and a.dept_id=21"
    df = read_sql(sql)
    if df.empty:
        table = fac.AntdEmpty()
    else:
        df.columns = df.columns.str.lower()
        df = df.sort_values("appdate", ascending=False)
        df["appdate"] = df["appdate"].dt.strftime("%m-%d")
        styleConditions = [
            {
                "condition": f"params.value == '{j}'",
                "style": {"backgroundColor": colors[i]},
            }
            for i, j in enumerate(df["project"].unique())
        ]

        table = dag.AgGrid(
            id=id("pcb-release-table"),
            className="ag-theme-quartz",
            columnDefs=[
                {
                    "field": "action",
                    "headerName": "ACTION",
                    "cellEditor": {"function": "DMC_Select"},
                    "cellEditorParams": {
                        "options": ["Release"],
                        "clearable": True,
                        "shadow": "xl",
                    },
                    "cellEditorPopup": True,
                    "singleClickEdit": True,
                    "width": 100,
                },
                {"headerName": "ID", "field": "id", "hide": True},
                {"field": "pcbpn", "headerName": "PCB PN", "width": 110},
                {"field": "prtno", "headerName": "PRTNO", "width": 130},
                {
                    "field": "project",
                    "headerName": "PROJECT",
                    "width": 220,
                    "cellStyle": {"styleConditions": styleConditions},
                },
                {"field": "board", "headerName": "BOARD", "width": 150},
                {"field": "qty", "headerName": "QTY", "width": 80},
                {"field": "pm", "headerName": "PM", "width": 110},
                {"field": "ee", "headerName": "EE", "width": 110},
                {"field": "me", "headerName": "ME", "width": 110},
                {"field": "appdate", "headerName": "APP.DATE", "width": 115},
            ],
            rowData=df.to_dict(orient="records"),
            defaultColDef={
                "editable": True,
                "resizable": True,
                "sortable": True,
                "filter": True,
            },
            dashGridOptions={
                "rowSelection": "single",
                "stopEditingWhenCellsLoseFocus": True,
                "singleClickEdit": True,
                "rowHeight": 35,
            },
            style={"height": "80vh"},
            getRowId="params.data.id",
        )

    tab = dmc.Container(
        dmc.Tabs(
            [
                dmc.TabsList(
                    [dmc.Tab("PCB Tree", value="1"), dmc.Tab("PCB Release", value="2")]
                ),
                # dmc.Space(h=5),
                dmc.TabsPanel(pcb_tree, value="1"),
                dmc.TabsPanel(table, value="2"),
            ],
            # color="red",
            value="1",
            id="tabs",
        ),
        fluid=True,
    )
    return tab


# ---------回调函数--------------
@callback(
    Output("pcb-upload", "disabled"),
    Input("pcb-model", "value"),
)
def enable_upload(value):
    if not value:
        raise PreventUpdate
    return False


@callback(
    Output("global-notice", "children"),
    Output("pcb-table", "rowData"),
    Output("pcb-table", "style"),
    Output("pcb-pm", "value"),
    Output("pcb-ee", "value"),
    Output("pcb-me", "value"),
    Output("pcb-mag", "value"),
    Input("pcb-model", "value"),
    Input("pcb-upload", "lastUploadTaskRecord"),
)
def select_model(model, upload):
    ctx_id = ctx.triggered_id
    style = Patch()
    if ctx_id == "pcb-model":
        if not model:
            raise PreventUpdate
        sql = "select * from pcb where model_name=%s"
        df = read_sql(sql, params=[model])
        if df.empty:
            style["display"] = "none"
            return no_update, [], style, *[None] * 4
        else:
            x = df[["pm", "ee", "me", "mag"]].iloc[0].tolist()

            style["display"] = "block"
            return (no_update, df.to_dict(orient="records"), style, *x)
    if ctx_id == "pcb-upload":
        if not model:
            return notice("请先选择Model", "error"), *[no_update] * 6
        file = (
            UPLOAD_FOLDER_ROOT
            / "pcbtree"
            / upload.get("taskId")
            / upload.get("fileName")
        )
        df = pd.read_excel(file, header=3, dtype=str, keep_default_na=False)
        df.columns = df.columns.str.lower().str.strip().str.replace("\s+", "_")
        df = df.rename(columns={"p/n": "pn", "qpa(单机用量)": "qpa"})
        if set(pcb_table_columns) - set(pcb_table_columns) != set():
            raise PreventUpdate

        df = df.loc[df["board_name"] != ""]
        x = [no_update] * 4

        style["display"] = "block"
        return no_update, df.to_dict(orient="records"), style, *x
    else:
        raise PreventUpdate


@callback(
    Output("global-notice", "children"),
    Output("pcbpn-download", "data"),
    Output("pcb-submit", "disabled"),
    Input("pcb-submit-confirm", "confirmCounts"),
    State("pcb-table", "rowData"),
    State("pcb-model", "value"),
    State("pcb-pm", "value"),
    State("pcb-ee", "value"),
    State("pcb-me", "value"),
    State("pcb-mag", "value"),
    State("user", "data"),
    State("pcb-upload", "lastUploadTaskRecord"),
)
def pcb_submit(confirm, data, model, pm, ee, me, mag, user, upload):
    if not confirm:
        raise PreventUpdate

    if not all([model, pm, ee, me, mag]):
        return notice("Model,PM,EE,ME,MAG不能为空", "error"), no_update, no_update

    if not upload:
        return notice("请上传PCB TREE文件", "error"), no_update, no_update

    df = pd.DataFrame(data, columns=pcb_table_columns)
    if not df["qpa"].astype(str).str.isnumeric().all():
        return notice("QPA需为数字", "error"), no_update, no_update

    now = datetime.now()
    nt_name = user.get("nt_name")
    df["model_name"] = model
    df["gmt_create"] = now
    df["pm"] = pm
    df["ee"] = ee
    df["me"] = me
    df["mag"] = mag
    df["layout"] = nt_name

    with pool.connection() as conn:
        with conn.cursor() as cu:
            cu.execute("select dept_id from ssp.user where nt_name=%s", [ee])
            res = cu.fetchone()

            dept_id = res["dept_id"]

            fileds = ",".join(df.columns)
            ph = ",".join(["%s"] * df.columns.size)
            params = df.values.tolist()

            sql = f"replace into ssp.pcb({fileds}) value({ph})"
            cu.executemany(sql, params)

            sql = "update ssp.pcb set pn=concat(%s,id) where pn=%s"
            cu.execute(sql, ["PCB", ""])
            conn.commit()

            sql = "select board_name as pcb,pn as pcbpn,ee,me,layout,qpa \
                from ssp.pcb where model_name=%s"
            df = read_sql(sql, params=[model])
            board = df.to_json(orient="records")

            sql = "insert into ssp.project(kick_off_date,mp_date,\
                owner,project,pm,ee,me,mag,layout,board,dept_id) \
                value(%s,%s,%s,%s,%s,%s,%s,%s,%s,%s,%s) ON DUPLICATE KEY UPDATE \
                owner=%s,pm=%s,ee=%s,me=%s,mag=%s,layout=%s,board=%s,dept_id=%s"
            cu.execute(
                sql,
                [
                    now,
                    now,
                    pm,
                    model,
                    pm,
                    ee,
                    me,
                    mag,
                    nt_name,
                    board,
                    dept_id,
                    pm,
                    pm,
                    ee,
                    me,
                    mag,
                    nt_name,
                    board,
                    dept_id,
                ],
            )
            conn.commit()

    to = f"{pm}@deltaww.com;{nt_name}@deltaww.com"
    subject = f"【PCB Tree 上传通知】{model}"
    body = df.to_html()
    bg_mail(to, subject, body)

    file = (
        UPLOAD_FOLDER_ROOT / "pcbtree" / upload.get("taskId") / upload.get("fileName")
    )
    wb = openpyxl.load_workbook(file)
    ws = wb.worksheets[0]
    for row in ws.iter_rows(min_row=5):
        for cell in row:
            if cell.col_idx == 3:
                board = ws.cell(cell.row, 2).value
                s = df.loc[df["pcb"] == board, "pcbpn"]
                if not s.empty:
                    ws[f"C{cell.row}"] = s.iat[0]
    wb.save(file)
    return notice("PCB料号申请提交成功"), dcc.send_file(file), True


@callback(
    Output("pcb-query-table", "data"),
    Output("pcb-query-table", "columns"),
    Input("tab-num", "active_tab"),
)
def pcb_query(active_tab):
    if active_tab != "tab-2":
        raise PreventUpdate

    sql = "select * from ssp.pcb order by id desc limit 500"
    df = read_sql(sql)
    df = df.drop("id", axis=1)

    data = df.to_dict(orient="records")
    columns = [{"name": i, "id": i} for i in df.columns]
    return data, columns


@callback(
    Output("pcbpn-download", "data"),
    Input("pcbpn-download-btn", "n_clicks"),
)
def pcbpn_download(n):
    return dcc.send_file(
        SSP_DIR / "3. Document backup" / "ssp_doc" / "PCB临时PN使用教程.pptx"
    )


@callback(
    Output("pcbpn-download", "data"),
    Input("pcbtree-download-btn", "n_clicks"),
)
def pcb_tree_download(n):
    return dcc.send_file(SSP_DIR / "3. Document backup" / "ssp_doc" / "PCB Tree.xlsx")


@callback(
    Output(id("pcb-release-table"), "rowTransaction"),
    Input(id("pcb-release-table"), "cellValueChanged"),
    State("user", "data"),
)
def pcb_release(data, user):
    if not data:
        raise PreventUpdate
    data = data[0]
    colId = data["colId"]
    if colId != "action":
        raise PreventUpdate
    nt_name = user.get("nt_name")
    d = data["data"]

    # -------------【PCB Mail】-----------------
    subject = f"【PCB Mail】{d['dept']}|{d['proj']},项目号:{d['prtno']},PCB:{d['pcbpn']}-{d['pcbstatus']},样制数量:{d['qty']}pcs"
    to = [nt_name, d["ee"], d["me"], d["pm"]] + cfg.pcb_mail + cfg.des_layout
    to = ";".join(f"{i}@deltaww.com" for i in to)

    dfb = pd.DataFrame(
        [{}],
        columns=[
            "机种",
            "PWB料号",
            "工程师",
            "描述",
            "数量(PCS)",
            "是否雙面SMD連片數量",
            "样制",
            "复投",
            "是否加急",
        ],
    )
    dfb["描述"] = (
        "Eg:4層板 T=0mm（板厚） 3OZ（銅厚） OSP（表面處理） FR-4（板材）0*0（連片尺寸）"
    )
    dfb["是否雙面SMD連片數量"] = "Eg:2S2P"
    dfb = dfb.fillna("")
    body_table = dfb.to_html(index=False)
    body = f"""
        <B>Dear {nt_name}:
        <Font Color=red><strong style=background:yellow>
        <p>1.请勿修改主题
        <p>2.请在此邮件上全部回复，并附上买板资料(Gerber,PNP,连片图)
        <p>3.如您非该板Layout，请将邮件转发给对应Layout，谢谢
        {body_table}
        """
    bg_mail(to, subject, body)

    # -------------更新PCB Release Date-----------------
    with pool.connection() as conn:
        with conn.cursor() as cu:
            sql = "update ssp.prt set layout=%s,pcb_release_date=now() where id=%s"
            cu.execute(sql, [nt_name, d["id"]])
            conn.commit()

    return {"remove": [d]}
