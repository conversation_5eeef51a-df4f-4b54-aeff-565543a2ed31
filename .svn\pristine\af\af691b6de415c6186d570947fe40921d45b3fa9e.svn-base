from pocketflow import Node, Flow
from .utils import call_llm


class ChatNode(Node):
    def prep(self, shared):
        return shared["message"]

    def exec(self, message):
        prompt = f"""
        你是电子生产制造计划安排专家，负责SMD生产计划安排
        请按以下要求，排定如下项目的最优SMD计划，给出每个项目的开始时间和结束时间
        项目清单：
        {message}
        排定要求：
        1.从2025年6月6日开始排
        2.贴片设备有MY300和BM221两台
        3.不同设备独立安排项目，合理分配项目，充分利用好设备
        4.MY300适合上小点数项目，BM221适合上大点数项目
        5.早上9点开始，下午4点30下班，这之间的时间可以安排项目
        6.每台设备最多能上120种材料
        7.项目之间半小时间隔切换项目
        """
        response = call_llm(prompt)
        return response

    def post(self, shared, prep_res, exec_res):
        shared["result"] = exec_res
        return


def smd_plan(shared):
    chat_node = ChatNode()
    flow = Flow(start=chat_node)
    flow.run(shared)
    return shared
