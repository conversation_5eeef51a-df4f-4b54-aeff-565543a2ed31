import instructor
from pydantic import BaseModel, Field
from openai import OpenAI
import os
from datetime import datetime
from enum import Enum


class PlacerType(str, Enum):
    MY300 = "MY300"
    BM221 = "BM221"


# Define your desired output structure
class SmdPlan(BaseModel):
    prtno: str = Field(description="项目号")
    placer: PlacerType = Field(description="设备，只能是 MY300 或 BM221")
    start_date: datetime = Field(description="贴片开始时间")
    end_date: datetime = Field(description="贴片完成时间")


def log_kwargs(**kwargs):
    print(f"Function called with kwargs: {kwargs}")


def log_exception(exception: Exception):
    print(f"An exception occurred: {str(exception)}")


api_key = "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"
base_url = "https://openrouter.ai/api/v1"
model = "google/gemini-2.5-flash-preview-05-20"
# model = "google/gemini-2.5-flash-preview-05-20:thinking"

# api_key = "sk-amvaicfhwwibhnrnwrbudcwzdhwnphkasglgqkydapfmyoav"
# base_url = "https://api.siliconflow.cn/v1"
# model = "Pro/deepseek-ai/DeepSeek-V3"

# client.on("completion:kwargs", log_kwargs)
# client.on("completion:error", log_exception)


def call_llm1(prompt):
    print(prompt)

    client = OpenAI(api_key=api_key, base_url=base_url)
    r = client.chat.completions.create(
        model=model, messages=[{"role": "user", "content": prompt}]
    )
    return r.choices[0].message.content


def call_llm(prompt):
    client = instructor.from_openai(
        OpenAI(api_key=api_key, base_url=base_url),
        mode=instructor.Mode.JSON,
    )
    r = client.chat.completions.create_iterable(
        model=model,
        response_model=SmdPlan,
        messages=[{"role": "user", "content": prompt}],
    )
    # 将Pydantic模型列表转换为字典列表
    dict_list = [item.model_dump() for item in r]
    # 转换为DataFrame
    return dict_list
