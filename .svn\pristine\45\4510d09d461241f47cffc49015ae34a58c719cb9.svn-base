import os
from datetime import date, datetime, timedelta
from enum import Enum

import instructor
from openai import OpenAI
from pydantic import BaseModel, Field, model_validator

# from pydantic_ai import Agent
# from pydantic_ai.models.openai import OpenAIModel
# from pydantic_ai.providers.openai import OpenAIProvider
# from pydantic_ai.providers.openrouter import OpenRouterProvider


class PlacerType(str, Enum):
    MY300 = "MY300"
    BM221 = "BM221"
    W2 = "W2"


# Define your desired output structure
class SmdPlanList(BaseModel):
    project: str = Field(description="项目名称")
    placer: PlacerType = Field(description="贴片机")
    start_time: datetime = Field(description="计划开始时间")
    end_time: datetime = Field(description="计划完成时间")
    earliest_start_date: date = Field(
        description="最早可开始日期(直接来源于项目清单,不能修改)"
    )
    latest_start_date: date = Field(
        description="最晚需开始日期(直接来源于项目清单,不能修改)"
    )
    leadtime: float = Field(description="贴片用时(直接来源于项目清单,不能修改)")
    previous_end_time: datetime = Field(
        description="同一台贴片机上前一个项目的结束时间"
    )

    # --- 添加的约束验证逻辑 ---
    @model_validator(mode="after")
    def check_start_time_not_earlier_than_earliest(self) -> "SmdPlanList":
        """
        验证: 计划开始时间不能早于最早可开始日期
        """
        # 由于 start_date 是 datetime 类型, earliest_start_date 是 date 类型,
        # 我们需要将 start_date 转换为 date 类型进行比较。
        if self.start_time.date() < self.earliest_start_date:
            # 如果验证失败，抛出 ValueError。Pydantic 会捕获它并生成一个 ValidationErrror。
            raise ValueError(
                "计划开始时间 (start_time) 不能早于最早可开始日期 (earliest_start_date)"
            )
        if self.end_time - self.start_time < timedelta(hours=self.leadtime):
            raise ValueError(
                "计划完成时间 (end_time) 和开始时间 (start_time) 之间的间隔不能小于贴片用时 (leadtime)"
            )

        # 如果验证通过，必须返回 self
        return self


class SmdPlan(BaseModel):
    analysis: str = Field(description="分析过程,要求中文")
    schedule: list[SmdPlanList] = Field(description="SMD计划列表")
    unplanned: list[str] = Field(
        description="无法安排的项目列表，尽量不要有无法安排的项目"
    )


# api_key = "sk-amvaicfhwwibhnrnwrbudcwzdhwnphkasglgqkydapfmyoav"
# base_url = "https://api.siliconflow.cn/v1"
# model = "Pro/deepseek-ai/DeepSeek-R1"
# model = "Qwen/Qwen3-30B-A3B"


def call_llm1(prompt):
    api_key = os.getenv("OPENROUTER_API_KEY")
    base_url = "https://openrouter.ai/api/v1"
    model = "google/gemini-2.5-flash-preview-05-20"

    client = OpenAI(api_key=api_key, base_url=base_url)
    r = client.chat.completions.create(
        model=model, messages=[{"role": "user", "content": prompt}]
    )
    return r.choices[0].message.content


def call_llm(prompt):
    api_key = os.getenv("OPENROUTER_API_KEY")
    base_url = "https://openrouter.ai/api/v1"
    model = "google/gemini-2.5-flash-preview-05-20"
    # model = "google/gemini-2.5-flash-preview-05-20:thinking"
    print(prompt, model)
    client = instructor.from_openai(
        OpenAI(api_key=api_key, base_url=base_url),
        mode=instructor.Mode.JSON,
    )
    r = client.chat.completions.create(
        model=model,
        response_model=SmdPlan,
        messages=[{"role": "user", "content": prompt}],
    )
    # dict_list = [item.model_dump() for item in r]
    # print(dict_list)
    return r


# def call_llm2(prompt):
#     model = OpenAIModel(
#         "google/gemini-2.5-flash-preview-05-20",
#         provider=OpenRouterProvider(api_key=os.getenv("OPENROUTER_API_KEY")),
#     )
#     # model = OpenAIModel(
#     #     "deepseek/deepseek-r1-0528:free",
#     #     provider=OpenRouterProvider(api_key=os.getenv("OPENROUTER_API_KEY")),
#     # )
#     # api_key = os.getenv("OPENROUTER_API_KEY")
#     # base_url = "https://openrouter.ai/api/v1"
#     # model = "google/gemini-2.5-flash-preview-05-20"
#     # model = OpenAIModel(
#     #     model,
#     #     provider=OpenAIProvider(base_url=base_url, api_key=api_key),
#     # )
#     agent = Agent(model, output_type=SmdPlan, retries=3)
#     result = agent.run_sync(prompt)
#     return result.output
