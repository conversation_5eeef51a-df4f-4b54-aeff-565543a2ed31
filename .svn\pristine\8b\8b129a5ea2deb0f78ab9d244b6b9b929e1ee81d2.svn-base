from pocketflow import Node, Flow
from .utils import call_llm
from datetime import datetime


class ChatNode(Node):
    def prep(self, shared):
        return shared["message"]

    def exec(self, message):
        now = datetime.now().date()
        prompt = f"""
请您扮演一名SMD生产计划排程专家，负责为SMD产线安排生产计划。您的目标是充分利用工作时间和设备资源，生成一个最优化的生产计划方案。

一、项目清单:
{message}

请您严格遵循以下原则和约束进行排产：
计划起始日期：
    {now}，所有项目的排产将从这一天开始考虑。
项目最早可开始日期:
    计划开始日期不能小于最早可开始日期

前置条件： 
    贴片开始时间不能早于项目的“板齐日期”和“料齐日期”中的任意一个。
设备资源：
    共有3台贴片机：MY300, BM221, W2。
    不同设备可以独立安排项目，请合理分配项目，确保设备利用率最大化。
设备特性：
    MY300： 适合生产点数少的项目。
    BM221： 适合生产点数多的项目。
    W2： 适合生产点数多的项目。
工作时间：
    每日工作时间为 早上9:00至下午16:30。
    午休时间（11:30至13:30） 不可安排生产。
换线时间：
    同一台设备上，不同项目之间需要预留半小时的切换间隔时间。
请根据上述要求，并结合项目清单，生成详细的SMD生产计划。
        """
        response = call_llm(prompt)
        return response

    def post(self, shared, prep_res, exec_res):
        shared["result"] = exec_res


def smd_plan(shared):
    chat_node = ChatNode()
    flow = Flow(start=chat_node)
    flow.run(shared)
    return shared
