# -*- coding: utf-8 -*-
import time

import dash_bootstrap_components as dbc
import dash_mantine_components as dmc
import feffery_antd_components.alias as fac
import feffery_utils_components.alias as fuc
from dash import Patch, no_update, set_props
from dash.exceptions import PreventUpdate
from dash_extensions.enrich import Input, Output, State, callback, html
from dash_iconify import DashIconify
from dash_player import DashPlayer

from common import id_factory, read_sql, parse_search
from config import pool
from tasks import task_put_on_shelf, task_take_off_shelf
from diskcache import Index

id = id_factory(__name__)
focus_shelf = """
document.getElementById("pages-stock-pda-layout-shelf").focus()
"""
focus_tray = """
document.getElementById("pages-stock-pda-layout-tray").focus();
"""
focus_picking = """
document.getElementById("pages-stock-pda-layout-picking").focus();
"""
focus_picking2 = """
document.getElementById("pages-stock-pda-layout-picking-deltapn").focus();
"""

focus_supply = """
document.getElementById("pages-stock-pda-layout-supply").focus();
"""
focus_fix = """
document.getElementById("pages-stock-pda-layout-fix-id").focus();
"""


def layout(user: dict, page: str = "", prtno: str = "", **kwargs):
    set_props("page-header", {"style": {"display": "none"}})
    if page == "picking":
        content = dmc.Stack(
            [
                dmc.RadioGroup(
                    [
                        dmc.Radio(label="按标签", value="按标签", color="red"),
                        dmc.Radio(label="按料号", value="按料号", color="blue"),
                    ],
                    id=id("picking-type"),
                    value="按标签",
                    label="领料",
                ),
                dbc.Input(
                    id=id("picking"),
                    placeholder="扫描领料标签二维码",
                    debounce=True,
                    autoFocus=True,
                ),
                dmc.Stack(
                    [
                        dbc.Input(
                            id=id("picking-deltapn"),
                            placeholder="扫描或输入料号",
                            debounce=True,
                            autoFocus=True,
                        ),
                        dbc.Input(
                            id=id("picking-qty"),
                            placeholder="输入数量",
                            debounce=True,
                            autoFocus=True,
                            type="number",
                        ),
                        dmc.Button("提交", id=id("picking-submit")),
                    ],
                    id=id("picking2"),
                    # spacing=20,
                ),
            ]
        )
    elif page == "bulk":
        content = dmc.Stack(
            [
                dmc.Title(f"{prtno}散料核对", size="h3"),
                dbc.Input(
                    id=id("bulk"),
                    placeholder="扫描料号二维码",
                    debounce=True,
                    autoFocus=True,
                    key=f"{prtno}",
                ),
            ]
        )
    elif page == "supply":
        content = dmc.Stack(
            [
                dmc.Text("补料"),
                dbc.Input(
                    id=id("supply"),
                    placeholder="扫描旧盘标签二维码",
                    debounce=True,
                    autoFocus=True,
                ),
                # dbc.Input(
                #     id=id("supply-qty"),
                #     placeholder="输入数量",
                #     debounce=True,
                #     type="number",
                # ),
                # dmc.Button("提交", id=id("supply-submit")),
            ]
        )
    elif page == "fix":
        content = dmc.Stack(
            [
                dmc.Text("修正数量(数量输入为0时,将删除该料盘)"),
                # dmc.Divider(),
                dbc.Input(
                    id=id("fix-id"),
                    placeholder="扫描料盘标签二维码",
                    debounce=True,
                    autoFocus=True,
                ),
                dbc.Input(
                    id=id("fix-qty"),
                    placeholder="输入数量",
                    debounce=True,
                    type="number",
                ),
                dmc.Button("提交", id=id("fix-submit")),
            ]
        )
    elif page == "check":
        content = dmc.Stack(
            [
                dmc.Text("料架检查"),
                dbc.Input(
                    id=id("check-shelf"),
                    placeholder="扫料架二维码,留空默认检查所有料架",
                    debounce=True,
                    autoFocus=True,
                ),
                dmc.Switch(
                    id=id("empty"),
                    label="空库位亮灯",
                    color="orange",
                    onLabel=dmc.Text("打开", size=13),
                    offLabel=dmc.Text("关闭", size=13),
                    size="lg",
                    styles={"track": {"width": "100px"}},
                ),
                dmc.Switch(
                    id=id("not-empty"),
                    label="有库位亮灯",
                    color="green",
                    onLabel=dmc.Text("打开", size=13),
                    offLabel=dmc.Text("关闭", size=13),
                    size="lg",
                    styles={"track": {"width": "100px"}},
                ),
                # dmc.Switch(
                #     id=id("all-light"),
                #     label="全部亮灯",
                #     color="green",
                #     onLabel=dmc.Text("打开", size=13),
                #     offLabel=dmc.Text("关闭", size=13),
                #     size="lg",
                #     styles={"track": {"width": "100px"}},
                # ),
                dmc.Button(
                    "全部亮灯",
                    leftIcon=DashIconify(
                        icon="mdi:lightbulb-on", width=30, color="green"
                    ),
                    w=200,
                    variant="filled",
                    color="green",
                    id=id("light-on"),
                ),
                dmc.Button(
                    "全部灭灯",
                    leftIcon=DashIconify(
                        icon="material-symbols:light-off", width=30, color="gray"
                    ),
                    w=200,
                    variant="filled",
                    color="gray",
                    id=id("light-off"),
                ),
            ],
            spacing=30,
        )
    elif page == "inventory":
        content = dmc.Stack(
            [
                dmc.Text("盘点"),
                # EventListener(
                #     id=id("keyboard"),
                # ),
                dbc.Input(
                    id=id("inventory-id"),
                    placeholder="扫描料盘标签二维码",
                    debounce=True,
                    autoFocus=True,
                ),
                # dbc.Input(
                #     id=id("inventory-qty"),
                #     placeholder="输入数量",
                #     # debounce=True,
                #     type="number",
                # ),
                # dmc.Button("提交", id=id("inventory-submit")),
                # dcc.Store(id=id("store"), data={}),
                # dbc.ListGroup(id=id("inventory-list"), numbered=True),
            ]
        )
    else:
        content = dmc.Stack(
            [
                dmc.Text("上架"),
                dbc.InputGroup(
                    [
                        dbc.InputGroupText("起始架位"),
                        dbc.Input(
                            id=id("shelf"),
                            placeholder="扫描起始架位二维码",
                            debounce=True,
                            autoFocus=True,
                        ),
                    ],
                ),
                dbc.Input(
                    id=id("tray"),
                    placeholder="扫描料盘二维码",
                    debounce=True,
                    style={"display": "none"},
                ),
            ],
            id=id("content"),
        )
    return dmc.Container(
        [
            dbc.Alert(id=id("alert"), color="success", is_open=False, fade=True),
            content,
            fuc.ExecuteJs(id=id("js")),
            DashPlayer(
                id=id("fail-audio"),
                url="/assets/fail.mp3",
                playing=False,
                controls=True,
                style={"display": "none"},
            ),
            # fuc.Fullscreen(
            #     id=id("fullscreen"), targetId=id("content"), isFullscreen=True
            # ),
            # dmc.Space(h=50),
            # fuc.QRCode(value="http://sup.deltaww.com/pda"),
            # dmc.Text("PDA二维码"),
            # dmc.Container(id=id("keyboard-container")),
            # dmc.Group(
            #     [
            #         fuc.QRCode(value="0011001"),
            #         fuc.QRCode(value="Ry210510102118151643"),
            #         fuc.QRCode(value="Ry220707105525341357"),
            #     ],
            #     position="apart",
            # ),
            fac.FloatButtonGroup(
                [
                    fac.FloatButton(
                        description="上架", href="/pda?page=shelf", target="_self"
                    ),
                    fac.FloatButton(
                        description="领料", href="/pda?page=picking", target="_self"
                    ),
                    fac.FloatButton(
                        description="补料", href="/pda?page=supply", target="_self"
                    ),
                    fac.FloatButton(
                        description="修正", href="/pda?page=fix", target="_self"
                    ),
                    # fac.FloatButton(
                    #     description="盘点", href="/pda?page=inventory", target="_self"
                    # ),
                    fac.FloatButton(
                        description="自检", href="/pda?page=check", target="_self"
                    ),
                ],
                trigger="click",
                icon=fac.Icon(icon="antd-menu"),
                type="primary",
                # style={"width": "100px", "height": "100px"},
            ),
        ]
    )


@callback(
    Output(id("tray"), "style"),
    Output(id("js"), "jsString"),
    Output(id("shelf"), "disabled"),
    Input(id("shelf"), "value"),
)
def shelf_submit(value):
    if not value:
        raise PreventUpdate
    with pool.connection() as conn:
        with conn.cursor() as cu:
            sql = "select id from ssp.stockno_list where stockno=%s and type=%s limit 1"
            cu.execute(sql, [value, "电子料架"])
            res = cu.fetchone()
            if res:
                set_props(
                    id("alert"),
                    {
                        "children": f"起始架位:{value},请扫描料盘码,然后放入亮灯的架位",
                        "is_open": True,
                        "color": "success",
                    },
                )
                return {"display": "block"}, focus_tray, True
            else:
                set_props(
                    id("alert"),
                    {
                        "children": "输入错误，该库位非电子料架",
                        "is_open": True,
                        "color": "danger",
                    },
                )
                set_props(id("fail-audio"), {"playing": True, "seekTo": 0})
                return {"display": "none"}, no_update, no_update


@callback(
    Output(id("tray"), "value"),
    Output(id("js"), "jsString"),
    Input(id("tray"), "value"),
    State(id("shelf"), "value"),
    State("user", "data"),
    running=[
        (Output(id("tray"), "disabled"), True, False),
    ],
)
def tray_submit(tray, shelf, user):
    if not tray:
        raise PreventUpdate
    if tray.startswith("R"):
        tray = tray[1:]

    with pool.connection() as conn:
        with conn.cursor() as cu:
            sql = "select id,stock_id,qty from ssp.stock_uid where uid=%s limit 1"
            cu.execute(sql, [tray])
            res = cu.fetchone()
            if not res:
                set_props(
                    id("alert"),
                    {
                        "children": f"{tray}未注册,请补标签",
                        "is_open": True,
                        "color": "danger",
                    },
                )
                set_props(id("fail-audio"), {"playing": True, "seekTo": 0})
                return "", focus_tray

            qty = res.get("qty") or 1000
            stock_id = res.get("stock_id")
            insert_stock_in_out = True

            # 判断该料总数量是否为0
            # TODO只要待出库有数量就能上架？不然后面一个项目用完了还会不能上架的
            sql = "SELECT qty FROM ssp.stock WHERE id=%s"
            cu.execute(sql, [stock_id])
            res = cu.fetchone()
            if res:
                if res["qty"] == 0:
                    sql = "select id from stockout where stock_id=%s and lable is null"
                    cu.execute(sql, [stock_id])
                    res = cu.fetchone()
                    if not res:
                        set_props(
                            id("alert"),
                            {
                                "children": "该材料总数量已为0,无法上架",
                                "is_open": True,
                                "color": "danger",
                            },
                        )
                        set_props(id("fail-audio"), {"playing": True, "seekTo": 0})
                        return "", focus_tray

            sql = "SELECT uid,SUM(qty) AS qty FROM stock_in_out \
                WHERE uid=%s GROUP BY uid"
            cu.execute(sql, [tray])
            res = cu.fetchone()
            if res:
                insert_stock_in_out = False
                if res["qty"] <= 0:
                    set_props(
                        id("alert"),
                        {
                            "children": "该料盘数量已为0,无法上架",
                            "is_open": True,
                            "color": "danger",
                        },
                    )
                    set_props(id("fail-audio"), {"playing": True, "seekTo": 0})
                    return "", focus_tray

            sql = "select stockno from ssp.stockno_list where uid=%s limit 1"
            cu.execute(sql, [tray])
            res = cu.fetchone()
            if res:
                stockno = res.get("stockno")
                # task_put_on_shelf.call_local(stockno)
                set_props(
                    id("alert"),
                    {
                        "children": f"该料盘已上架,请放入{stockno}架位",
                        "is_open": True,
                        "color": "danger",
                    },
                )
                set_props(id("fail-audio"), {"playing": True, "seekTo": 0})
                return "", focus_tray

            sql = "select id,stockno from ssp.stockno_list \
                where type=%s and stockno like %s \
                    AND stockno >=%s \
                        and uid is null order by stockno limit 1"
            cu.execute(sql, ["电子料架", f"{shelf[:3]}%", shelf])
            res = cu.fetchone()

            if not res:
                set_props(
                    id("alert"),
                    {
                        "children": "该架位无可用空位,请更换架位",
                        "is_open": True,
                        "color": "danger",
                    },
                )
                set_props(id("fail-audio"), {"playing": True, "seekTo": 0})
                return "", focus_tray

            stockno = res.get("stockno")
            stockno_id = res.get("id")
            sql = "update ssp.stockno_list set uid=%s,stock_id=%s where id=%s"
            cu.execute(sql, [tray, stock_id, stockno_id])

            sql = "update ssp.stock set stockno=%s where id=%s"
            cu.execute(sql, [stockno, stock_id])

            if insert_stock_in_out:
                nt_name = user.get("nt_name")
                sql = "insert into ssp.stock_in_out\
                    (uid,stock_id,stockno_id,qty,owner,type) \
                    values(%s,%s,%s,%s,%s,%s)"
                cu.execute(sql, [tray, stock_id, stockno_id, qty, nt_name, "首次"])
            conn.commit()

            res = task_put_on_shelf.call_local(stockno)
            if res.get("RetCode") != 1:
                set_props(
                    id("alert"),
                    {
                        "children": f"亮灯失败，请将该料盘放入架位:{stockno}",
                        "is_open": True,
                        "color": "danger",
                    },
                )
                set_props(id("fail-audio"), {"playing": True, "seekTo": 0})
            else:
                set_props(
                    id("alert"),
                    {
                        "children": f"请将该料盘放入架位:{stockno}",
                        "is_open": True,
                        "color": "success",
                    },
                )
            return "", focus_tray


@callback(
    Output(id("picking"), "style"),
    Output(id("picking2"), "style"),
    Output(id("js"), "jsString"),
    Input(id("picking-type"), "value"),
    prevent_initial_call=False,
)
def picking_type(value):
    if not value:
        raise PreventUpdate

    if value == "按标签":
        return {"display": "block"}, {"display": "none"}, focus_picking
    else:
        return {"display": "none"}, {"display": "flex"}, focus_picking2


@callback(
    Output(id("picking"), "value"),
    Output(id("js"), "jsString"),
    Input(id("picking"), "value"),
    State("user", "data"),
    running=[
        (Output(id("picking"), "disabled"), True, False),
    ],
)
def picking_submit(value, user):
    if not value:
        raise PreventUpdate
    if value.count("{") != 2:
        set_props(
            id("alert"),
            {
                "children": "输入不正确,请重试",
                "is_open": True,
                "color": "danger",
            },
        )
        set_props(id("fail-audio"), {"playing": True, "seekTo": 0})
        return "", focus_picking

    checkcode, qty, _ = value.split("{")
    with pool.connection() as conn:
        with conn.cursor() as cu:
            sql = "select id,uid,stockno,stock_id from stockno_list \
                where type=%s and block is null and stock_id=\
                (select id from ssp.stock where checkcode=%s and area=%s) \
                order by uid limit 1"
            cu.execute(sql, ["电子料架", checkcode, "SH"])
            res = cu.fetchone()
            if not res:
                set_props(
                    id("alert"),
                    {
                        "children": f"{checkcode}不在电子料架上,或数量不足",
                        "is_open": True,
                        "color": "danger",
                    },
                )
                set_props(id("fail-audio"), {"playing": True, "seekTo": 0})
                return "", focus_picking
            stockno = res.get("stockno")
            uid = res.get("uid")
            stock_id = res.get("stock_id")
            stockno_id = res.get("id")
            nt_name = user.get("nt_name")

            # 检查是否重复扫码
            sql = "select id from stock_in_out \
                where stock_id=%s and type=%s \
                and TIMESTAMPDIFF(SECOND,gmt_create,NOW())<65"
            cu.execute(sql, [stock_id, "领料"])
            res = cu.fetchone()
            if res:
                set_props(
                    id("alert"),
                    {
                        "children": "是否重复扫码?如非重复,请等待60秒后重试",
                        "is_open": True,
                        "color": "danger",
                    },
                )
                set_props(id("fail-audio"), {"playing": True, "seekTo": 0})
                return "", focus_picking

            sql = "insert into ssp.stock_in_out\
                (uid,stock_id,stockno_id,qty,owner,type) \
                values(%s,%s,%s,%s,%s,%s)"
            cu.execute(sql, [uid, stock_id, stockno_id, -int(qty), nt_name, "领料"])
            sql = "update ssp.stockno_list set uid=null,stock_id=null where uid=%s"
            cu.execute(sql, [uid])
            conn.commit()
            task_take_off_shelf([stockno], color="Yellow")
            task_take_off_shelf.schedule(([stockno], "Gray"), delay=60)
            set_props(
                id("alert"),
                {
                    "children": f"{checkcode}在{stockno}上,请取料",
                    "is_open": True,
                    "color": "success",
                },
            )
            return "", focus_picking


@callback(
    Output(id("picking-deltapn"), "value"),
    Output(id("picking-qty"), "value"),
    Output(id("js"), "jsString"),
    Input(id("picking-submit"), "n_clicks"),
    State(id("picking-deltapn"), "value"),
    State(id("picking-qty"), "value"),
    State("user", "data"),
    running=[
        (Output(id("picking-submit"), "disabled"), True, False),
    ],
)
def picking_by_deltapn(n_clicks, checkcode, qty, user):
    if not n_clicks:
        raise PreventUpdate

    if not checkcode or not qty:
        set_props(
            id("alert"),
            {
                "children": "请输入料号和数量",
                "is_open": True,
                "color": "danger",
            },
        )
        set_props(id("fail-audio"), {"playing": True, "seekTo": 0})
        return no_update, no_update, focus_picking2

    with pool.connection() as conn:
        with conn.cursor() as cu:
            sql = "select id,uid,stockno,stock_id from stockno_list \
                where type=%s and block is null and stock_id=\
                (select id from ssp.stock where checkcode=%s and area=%s) \
                order by uid limit 1"
            cu.execute(sql, ["电子料架", checkcode, "SH"])
            res = cu.fetchone()
            if not res:
                set_props(
                    id("alert"),
                    {
                        "children": f"{checkcode}不在电子料架上,或数量不足",
                        "is_open": True,
                        "color": "danger",
                    },
                )
                set_props(id("fail-audio"), {"playing": True, "seekTo": 0})
                return "", "", focus_picking2

            stockno = res.get("stockno")
            uid = res.get("uid")
            stock_id = res.get("stock_id")
            stockno_id = res.get("id")
            nt_name = user.get("nt_name")

            # 检查是否重复扫码
            sql = "select id from stock_in_out \
                where stock_id=%s and type=%s \
                and TIMESTAMPDIFF(SECOND,gmt_create,NOW())<65"
            cu.execute(sql, [stock_id, "领料"])
            res = cu.fetchone()
            if res:
                set_props(
                    id("alert"),
                    {
                        "children": "是否重复扫码?如非重复,请等待60秒后重试",
                        "is_open": True,
                        "color": "danger",
                    },
                )
                set_props(id("fail-audio"), {"playing": True, "seekTo": 0})
                return "", "", focus_picking2

            sql = "insert into ssp.stock_in_out\
                (uid,stock_id,stockno_id,qty,owner,type) \
                values(%s,%s,%s,%s,%s,%s)"
            cu.execute(sql, [uid, stock_id, stockno_id, -int(qty), nt_name, "领料"])
            sql = "update ssp.stockno_list set uid=null,stock_id=null where uid=%s"
            cu.execute(sql, [uid])
            conn.commit()
            task_take_off_shelf([stockno], color="Yellow")
            task_take_off_shelf.schedule(([stockno], "Gray"), delay=60)
            set_props(
                id("alert"),
                {
                    "children": f"{checkcode}在{stockno}上,请取料",
                    "is_open": True,
                    "color": "success",
                },
            )
            return "", "", focus_picking2


@callback(
    Output(id("bulk"), "value"),
    Output(id("bulk"), "key"),
    Input(id("bulk"), "value"),
    State("url", "search"),
)
def bulk_submit(uid, url):
    if not uid:
        raise PreventUpdate
    url = parse_search(url)
    prtno = url.get("prtno")
    idx = Index(f"{prtno}")
    stockout_id = idx.pop(uid, default="料号错误")
    if stockout_id == "料号错误":
        set_props(
            id("alert"),
            {
                "children": "料号错误,或重复扫描",
                "is_open": True,
                "color": "danger",
            },
        )
        set_props(id("fail-audio"), {"playing": True, "seekTo": 0})
        return "", ""
    set_props(
        id("alert"),
        {
            "children": "料号正确",
            "is_open": True,
            "color": "success",
        },
    )
    with pool.connection() as conn:
        with conn.cursor() as cu:
            sql = "update ssp.stockout set StockOutDate2=now() where id in %s"
            cu.execute(sql, [stockout_id])

        conn.commit()
    return "", ""


@callback(
    Output(id("supply"), "value"),
    Output(id("js"), "jsString"),
    Input(id("supply"), "value"),
    State("user", "data"),
)
def supply_submit(uid, user):
    if not uid:
        raise PreventUpdate

    if uid.startswith("R"):
        uid = uid[1:]
    uid = uid.strip()

    with pool.connection() as conn:
        with conn.cursor() as cu:
            sql = "select * from ssp.stock_in_out \
                where uid=%s ORDER BY id DESC LIMIT 1"
            cu.execute(sql, [uid])
            res = cu.fetchone()
            if not res:
                set_props(
                    id("alert"),
                    {
                        "children": "料盘码不正确,或已删除",
                        "is_open": True,
                        "color": "danger",
                    },
                )
                set_props(id("fail-audio"), {"playing": True, "seekTo": 0})
                return "", focus_supply

            last_qty = res.get("qty")
            stock_id = res.get("stock_id")
            sql = "select SUM(qty) AS qty from ssp.stock_in_out \
                where uid =%s  GROUP BY uid"
            cu.execute(sql, [uid])
            res = cu.fetchone()
            sum_qty = res.get("qty")
            qty = sum_qty if sum_qty < 0 else -abs(last_qty)

            sql = "select id,uid,stockno from stockno_list \
                where stock_id=%s and type=%s order by uid limit 1"
            cu.execute(sql, [stock_id, "电子料架"])
            res = cu.fetchone()

            if not res:
                # sql = "delete from ssp.stock_uid where uid=%s"
                # cu.execute(sql, [uid])
                # sql = "update ssp.stockno_list set uid=null,stock_id=null where uid=%s"
                # cu.execute(sql, [uid])
                # conn.commit()

                msg = "料架上已没有该料号的料盘"
                sql = "select id from ssp.stock_in_out where stock_id=%s"
                cu.execute(sql, [stock_id])
                res1 = cu.fetchone()
                if not res1:
                    msg = msg + ",此为单盘材料,请修正库存数量和申请缺料"

                set_props(
                    id("alert"),
                    {
                        "children": msg,
                        "is_open": True,
                        "color": "danger",
                    },
                )
                set_props(id("fail-audio"), {"playing": True, "seekTo": 0})
                return "", focus_supply

            stockno = res.get("stockno")
            new_uid = res.get("uid")
            stockno_id = res.get("id")
            nt_name = user.get("nt_name")

            sql = "insert into ssp.stock_in_out\
                (uid,stock_id,stockno_id,qty,owner,type) \
                values(%s,%s,%s,%s,%s,%s)"
            cu.execute(sql, [new_uid, stock_id, stockno_id, qty, nt_name, "补料"])

            sql = "update ssp.stockno_list set uid=null,stock_id=null where uid=%s"
            cu.execute(sql, [new_uid])

            sql = "update stock_roll a \
                JOIN (SELECT id FROM stock_roll \
                WHERE uid=%s ORDER BY finished_date \
                DESC LIMIT 1)b ON a.id=b.id SET uid=%s"
            cu.execute(sql, [uid, new_uid])

            # sql = "delete from ssp.stock_uid where uid=%s"
            # cu.execute(sql, [uid])
            # sql = "update ssp.stockno_list set uid=null,stock_id=null where uid=%s"
            # cu.execute(sql, [uid])

            conn.commit()
            task_take_off_shelf([stockno], color="Yellow")
            task_take_off_shelf.schedule(([stockno], "Gray"), delay=60)
            set_props(
                id("alert"),
                {
                    "children": f"新料盘在{stockno}上,请取料",
                    "is_open": True,
                    "color": "success",
                },
            )
            return "", focus_supply


@callback(
    Output(id("fix-id"), "value"),
    Output(id("fix-qty"), "value"),
    Output(id("js"), "jsString"),
    Input(id("fix-submit"), "n_clicks"),
    State(id("fix-id"), "value"),
    State(id("fix-qty"), "value"),
    State("user", "data"),
    running=[
        (Output(id("fix-submit"), "disabled"), True, False),
    ],
)
def fix_submit(n_clicks, uid, qty, user):
    if not n_clicks:
        raise PreventUpdate

    if (not uid) or (qty is None):
        set_props(
            id("alert"),
            {
                "children": "请输入料盘码和数量",
                "is_open": True,
                "color": "danger",
            },
        )
        set_props(id("fail-audio"), {"playing": True, "seekTo": 0})
        return no_update, no_update, no_update

    if uid.startswith("R"):
        uid = uid[1:]
    uid = uid.strip()

    with pool.connection() as conn:
        with conn.cursor() as cu:
            sql = "SELECT uid,stock_id,SUM(qty) AS qty FROM stock_in_out \
                WHERE uid=%s GROUP BY uid"
            cu.execute(sql, [uid])
            res = cu.fetchone()
            if not res:
                set_props(
                    id("alert"),
                    {
                        "children": f"{uid}该料盘码有误或已删除，请核实",
                        "is_open": True,
                        "color": "danger",
                    },
                )
                set_props(id("fail-audio"), {"playing": True, "seekTo": 0})
                return "", no_update, focus_fix

            sum_qty = res.get("qty")
            stock_id = res.get("stock_id")
            nt_name = user.get("nt_name")
            if qty == 0:
                sql = "insert into stock_scrap\
                (id,roll_id,stock_id,stockno_id,uid,type,owner,qty,inventory_qty) \
                select id,roll_id,stock_id,stockno_id,uid,type,owner,qty,inventory_qty \
                from ssp.stock_in_out where uid=%s"
                cu.execute(sql, [uid])
                sql = "delete from ssp.stock_uid where uid=%s"
                cu.execute(sql, [uid])
                sql = "update ssp.stockno_list set uid=null,stock_id=null where uid=%s"
                cu.execute(sql, [uid])
                conn.commit()

                sql = "select id from ssp.stock_in_out where stock_id=%s"
                cu.execute(sql, [stock_id])
                res1 = cu.fetchone()
                if res1:
                    set_props(
                        id("alert"),
                        {
                            "children": "修正成功,该空盘已删除",
                            "is_open": True,
                            "color": "success",
                        },
                    )
                else:
                    set_props(
                        id("alert"),
                        {
                            "children": "修正成功,该空盘已删除,此为单盘材料,请修正库存数量和申请缺料",
                            "is_open": True,
                            "color": "danger",
                        },
                    )
                    set_props(id("fail-audio"), {"playing": True, "seekTo": 0})
            else:
                sql = "insert into ssp.stock_in_out\
                    (uid,stock_id,qty,inventory_qty,type,owner) \
                    values(%s,%s,%s,%s,%s,%s)"
                cu.execute(sql, [uid, stock_id, qty - sum_qty, qty, "盘点", nt_name])
                conn.commit()

                set_props(
                    id("alert"),
                    {
                        "children": "数量修正成功",
                        "is_open": True,
                        "color": "success",
                    },
                )
            return "", "", focus_fix


@callback(
    Output(id("store"), "data"),
    Output(id("inventory-qty"), "value"),
    Input(id("inventory-id"), "value"),
    State(id("store"), "data"),
)
def inventory_id_value(uid, store):
    if not uid:
        raise PreventUpdate
    if uid in store:
        return no_update, None

    if uid.startswith("R"):
        uid = uid[1:]
    uid = uid.strip()

    with pool.connection() as conn:
        with conn.cursor() as cu:
            sql = "SELECT distinct a.uid,ifnull(b.stockno,'空') as stockno,'空' as qty,\
                a.stock_id FROM stock_in_out a \
                LEFT JOIN stockno_list b ON a.uid=b.uid  \
                WHERE a.stock_id=(SELECT distinct stock_id FROM stock_in_out WHERE uid=%s)"
            cu.execute(sql, [uid])
            res = cu.fetchall()

            if not res:
                set_props(
                    id("alert"),
                    {
                        "children": "该料盘码有误，请核实",
                        "is_open": True,
                        "color": "danger",
                    },
                )
                set_props(id("inventory-id"), {"value": ""})
                return no_update, no_update
            stockno = [i["stockno"] for i in res if i["stockno"] != "空"]
            if stockno:
                task_take_off_shelf(stockno, color="Yellow")
            set_props(
                id("alert"),
                {
                    "children": "请输入盘点数量",
                    "is_open": True,
                    "color": "success",
                },
            )
            return {i["uid"]: i for i in res}, None


@callback(
    Output(id("store"), "data"),
    Input(id("inventory-qty"), "value"),
    State(id("inventory-id"), "value"),
)
def inventory_qty_value(qty, uid: str):
    if not qty or not uid:
        raise PreventUpdate

    if uid.startswith("R"):
        uid = uid[1:]
    uid = uid.strip()
    patch_data = Patch()
    patch_data[uid]["qty"] = qty
    return patch_data


@callback(
    Output(id("inventory-list"), "children"),
    Input(id("store"), "data"),
)
def inventory_list(data):
    if not data:
        return fac.Empty()
    return [
        dbc.ListGroupItem(
            f"料盘码:{i},架位:{j['stockno']},数量:{j['qty']}", color="info"
        )
        for i, j in data.items()
    ]


@callback(
    Output(id("inventory-id"), "value"),
    Output(id("inventory-qty"), "value"),
    Input(id("inventory-submit"), "n_clicks"),
    State(id("store"), "data"),
    State("user", "data"),
)
def inventory_submit(n_clicks, data: dict, user):
    if not n_clicks:
        raise PreventUpdate
    data = data.values()
    if any(i["qty"] == "空" for i in data):
        set_props(
            id("alert"),
            {
                "children": "所有料盘数量不能为空",
                "is_open": True,
                "color": "danger",
            },
        )
        return no_update, no_update

    total_qty = sum(i["qty"] for i in data)
    stock_id = list(data)[0]["stock_id"]
    stockno = [i["stockno"] for i in data if i["stockno"] != "空"]
    if stockno:
        task_take_off_shelf(stockno, color="Gray")

    with pool.connection() as conn:
        with conn.cursor() as cu:
            sql = "update ssp.stock set qty=%s where id=%s"
            cu.execute(sql, [total_qty, stock_id])
            for i in data:
                uid = i["uid"]
                qty = i["qty"]
                sql = "SELECT uid,stock_id,SUM(qty) AS qty FROM stock_in_out \
                    WHERE uid=%s GROUP BY uid"
                cu.execute(sql, [uid])
                res = cu.fetchone()
                sum_qty = res.get("qty")
                stock_id = res.get("stock_id")
                nt_name = user.get("nt_name")
                if qty == 0:
                    sql = "delete from ssp.stock_uid where uid=%s"
                    cu.execute(sql, [uid])
                    sql = "update ssp.stockno_list set uid=null,stock_id=null where uid=%s"
                    cu.execute(sql, [uid])
                else:
                    sql = "insert into ssp.stock_in_out\
                        (uid,stock_id,qty,inventory_qty,type,owner) \
                        values(%s,%s,%s,%s,%s,%s)"
                    cu.execute(
                        sql, [uid, stock_id, qty - sum_qty, qty, "盘点", nt_name]
                    )

        conn.commit()
        set_props(
            id("alert"),
            {
                "children": "修正成功",
                "is_open": True,
                "color": "success",
            },
        )
        return "", ""


@callback(
    Input(id("empty"), "checked"),
    State(id("check-shelf"), "value"),
    running=[
        (Output(id("empty"), "disabled"), True, False),
    ],
)
def empty_check(checked, shelf):
    if checked is None:
        raise PreventUpdate

    with pool.connection() as conn:
        with conn.cursor() as cu:
            if shelf:
                sql = "select stockno from stockno_list \
                    where uid is null and stockno like %s and type=%s"
                cu.execute(sql, [f"{shelf[:3]}%", "电子料架"])
            else:
                sql = "select stockno from stockno_list where uid is null and type=%s"
                cu.execute(sql, ["电子料架"])

            res = cu.fetchall()
            if res:
                stockno = [i["stockno"] for i in res]
                if checked:
                    task_take_off_shelf(stockno, color="Red")
                else:
                    task_take_off_shelf(stockno, color="Gray")
            else:
                raise PreventUpdate


@callback(
    Input(id("not-empty"), "checked"),
    State(id("check-shelf"), "value"),
    running=[
        (Output(id("not-empty"), "disabled"), True, False),
    ],
)
def not_empty_check(checked, shelf):
    if checked is None:
        raise PreventUpdate

    with pool.connection() as conn:
        with conn.cursor() as cu:
            if shelf:
                sql = "select stockno from stockno_list \
                    where uid is not null and stockno like %s and type=%s"
                cu.execute(sql, [f"{shelf[:3]}%", "电子料架"])
            else:
                sql = "select stockno from stockno_list \
                    where uid is not null and type=%s"
                cu.execute(sql, ["电子料架"])

            res = cu.fetchall()
            if res:
                stockno = [i["stockno"] for i in res]
                if checked:
                    task_take_off_shelf(stockno, color="Green")
                else:
                    task_take_off_shelf(stockno, color="Gray")
            else:
                raise PreventUpdate


@callback(
    Input(id("light-on"), "n_clicks"),
    State(id("check-shelf"), "value"),
)
def light_on(n_clicks, checkcode):
    if not n_clicks:
        raise PreventUpdate

    with pool.connection() as conn:
        with conn.cursor() as cu:
            if checkcode:
                sql = "select id from stock where checkcode=%s and area=%s"
                cu.execute(sql, [checkcode, "SH"])
                res = cu.fetchone()
                if res:
                    sql = "select stockno from stockno_list \
                        where type=%s and stock_id=%s"
                    params = ["电子料架", res["id"]]
                    cu.execute(sql, params)
                    res = cu.fetchall()
                    if res:
                        stockno = [i["stockno"] for i in res]
                        task_take_off_shelf(stockno, color="Green")
                    else:
                        raise PreventUpdate
                else:
                    raise PreventUpdate
            else:
                sql = "select stockno from stockno_list where type=%s"
                params = ["电子料架"]
                cu.execute(sql, params)
                res = cu.fetchall()
                if res:
                    stockno = [i["stockno"] for i in res]
                    task_take_off_shelf(stockno, color="Green")
                else:
                    raise PreventUpdate


@callback(
    Input(id("light-off"), "n_clicks"),
    State(id("check-shelf"), "value"),
)
def light_off(n_clicks, checkcode):
    if not n_clicks:
        raise PreventUpdate

    with pool.connection() as conn:
        with conn.cursor() as cu:
            if checkcode:
                sql = "select id from stock where checkcode=%s and area=%s"
                cu.execute(sql, [checkcode, "SH"])
                res = cu.fetchone()
                if res:
                    sql = "select stockno from stockno_list \
                        where type=%s and stock_id=%s"
                    params = ["电子料架", res["id"]]
                    cu.execute(sql, params)
                    res = cu.fetchall()
                    if res:
                        stockno = [i["stockno"] for i in res]
                        task_take_off_shelf(stockno, color="Gray")
                    else:
                        raise PreventUpdate
                else:
                    raise PreventUpdate
            else:
                sql = "select stockno from stockno_list where type=%s"
                cu.execute(sql, ["电子料架"])
                res = cu.fetchall()
                if res:
                    stockno = [i["stockno"] for i in res]
                    task_take_off_shelf(stockno, color="Gray")
                else:
                    raise PreventUpdate


def light_show():
    sql = "select stockno from stockno_list where type='电子料架' \
        and (stockno like '001%' or stockno like '003%' or stockno like '005%' or stockno like '007%')"
    df = read_sql(sql)
    df7 = df.loc[df["stockno"].str.startswith("007")]
    colors = ["Red", "Green", "Yellow", "Gray"]

    stockno = df["stockno"].tolist()
    for color in colors:
        task_take_off_shelf(stockno, color=color)
        time.sleep(3)

    stockno = df7["stockno"].tolist()

    for color in colors:
        task_take_off_shelf(stockno, color=color)
        time.sleep(3)

    for i in stockno[:100]:
        task_put_on_shelf(i, color="Red")
    for i in stockno[100:200]:
        task_put_on_shelf(i, color="Green")
    for i in stockno[200:300]:
        task_put_on_shelf(i, color="Yellow")
    for i in stockno[300:400]:
        task_put_on_shelf(i, color="Red")
    for i in stockno[400:500]:
        task_put_on_shelf(i, color="Green")
    for i in stockno[500:600]:
        task_put_on_shelf(i, color="Yellow")
    for i in stockno[600:700]:
        task_put_on_shelf(i, color="Red")
    for i in stockno[700:800]:
        task_put_on_shelf(i, color="Green")
